# RouKey Browser Automation Service

Advanced browser automation microservice with LangGraph orchestration and intelligent role routing.

## Features

- **LangGraph Hierarchical Orchestration**: Supervisor and hierarchical agent patterns
- **Browser Use Integration**: Full browser automation capabilities with memory
- **Intelligent Role Routing**: Dynamic role assignment based on task classification
- **Session Management**: Browser session pooling and resource optimization
- **Google Search Verification**: Cross-reference and validation system
- **Tier-Based Access Control**: Subscription-based feature and quota management
- **Real-time Streaming**: Progress updates and live task monitoring
- **Comprehensive Error Handling**: Multi-level fallback and recovery mechanisms

## Architecture

```
RouKey Frontend → Next.js API → Python Microservice (FastAPI + Browser Use) → Browser Automation
                                      ↓
                              Google Search API Integration
                                      ↓
                              Intelligent Role Routing
```

## Quick Start

### Using Docker Compose (Recommended)

1. **Clone and setup**:
   ```bash
   cd browser-automation-service
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

2. **Start services**:
   ```bash
   docker-compose up -d
   ```

3. **Test the service**:
   ```bash
   curl http://localhost:8000/health/
   ```

### Manual Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   playwright install chromium --with-deps
   ```

2. **Setup environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Redis and Qdrant** (required for session management and memory):
   ```bash
   # Redis
   docker run -d -p 6379:6379 redis:7-alpine
   
   # Qdrant
   docker run -d -p 6333:6333 qdrant/qdrant:latest
   ```

4. **Run the service**:
   ```bash
   uvicorn main:app --reload
   ```

## API Endpoints

### Browser Automation

- **POST** `/api/v1/browser/execute` - Execute browser automation task
- **POST** `/api/v1/browser/execute/stream` - Execute with streaming updates
- **GET** `/api/v1/browser/task/{task_id}/status` - Get task status
- **DELETE** `/api/v1/browser/task/{task_id}` - Cancel task

### Testing

- **POST** `/api/v1/test/browser-automation` - Test browser automation functionality
- **GET** `/api/v1/test/health-detailed` - Detailed health check
- **POST** `/api/v1/test/simulate-task` - Simulate task planning

### Health

- **GET** `/health/` - Basic health check
- **GET** `/health/ready` - Readiness check
- **GET** `/health/live` - Liveness check

## Configuration

### Environment Variables

Key configuration options:

```bash
# Service Configuration
SERVICE_HOST=localhost
SERVICE_PORT=8000
DEBUG=true

# RouKey Integration
ROKEY_API_URL=http://localhost:3000
ROKEY_API_SECRET=your-secret

# LLM API Keys
OPENAI_API_KEY=your-key
ANTHROPIC_API_KEY=your-key
GOOGLE_API_KEY=your-key

# Google Search API
GOOGLE_SEARCH_API_KEY=your-key
GOOGLE_SEARCH_ENGINE_ID=your-id

# Infrastructure
REDIS_URL=redis://localhost:6379/0
QDRANT_HOST=localhost
QDRANT_PORT=6333
```

### Tier Limits

- **Free**: No browser automation
- **Starter**: 15 tasks/month, max 30 steps
- **Pro**: Unlimited tasks, max 100 steps
- **Enterprise**: Unlimited tasks, max 200 steps

## Usage Example

```python
import httpx

# Execute browser automation task
response = httpx.post("http://localhost:8000/api/v1/browser/execute", json={
    "task": "Find the cheapest laptop on Amazon under $500",
    "user_id": "user_123",
    "config_id": "config_456",
    "user_tier": "pro",
    "routing_strategy": "complex_routing",
    "enable_verification": True,
    "max_steps": 50
})

result = response.json()
print(f"Task completed: {result['success']}")
print(f"Final result: {result['final_result']}")
```

## Integration with RouKey

This service integrates with RouKey's existing systems:

1. **Role Classification**: Uses RouKey's Gemini classifier for dynamic role assignment
2. **API Key Management**: Leverages RouKey's BYOK system for LLM access
3. **Routing Strategies**: Supports all RouKey routing strategies (complex, fallback, A/B testing)
4. **Subscription Tiers**: Enforces RouKey's tier-based access control
5. **Usage Tracking**: Reports usage back to RouKey for billing and analytics

## Development

### Project Structure

```
browser-automation-service/
├── app/
│   ├── api/routes/          # FastAPI routes
│   ├── core/                # Core configuration and utilities
│   ├── models/              # Pydantic models
│   └── services/            # Business logic services
├── main.py                  # FastAPI application entry point
├── requirements.txt         # Python dependencies
├── Dockerfile              # Container configuration
└── docker-compose.yml     # Multi-service setup
```

### Adding New Features

1. **New Agents**: Add to `app/services/langgraph_agents/`
2. **Custom Functions**: Extend `app/services/browser_functions/`
3. **API Endpoints**: Add to `app/api/routes/`
4. **Models**: Define in `app/models/`

## Monitoring and Logging

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Health Checks**: Kubernetes-ready health endpoints
- **Metrics**: Prometheus-compatible metrics (TODO)
- **Tracing**: Distributed tracing support (TODO)

## Security

- **API Authentication**: JWT-based authentication with RouKey
- **Rate Limiting**: Per-user and per-tier rate limits
- **Input Validation**: Comprehensive request validation
- **Browser Sandboxing**: Isolated browser contexts per session

## Performance

- **Session Pooling**: Efficient browser instance reuse
- **Async Processing**: Full async/await implementation
- **Memory Management**: Browser Use memory optimization
- **Caching**: Redis-based session and result caching

## Troubleshooting

### Common Issues

1. **Browser Launch Failures**:
   ```bash
   # Install missing dependencies
   playwright install-deps chromium
   ```

2. **Redis Connection Issues**:
   ```bash
   # Check Redis is running
   docker ps | grep redis
   ```

3. **Memory Issues**:
   ```bash
   # Increase Docker memory limits
   # Check browser pool configuration
   ```

### Logs

View service logs:
```bash
# Docker Compose
docker-compose logs -f browser-automation

# Direct
tail -f logs/browser-automation.log
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This project is part of the RouKey platform and follows the same licensing terms.
