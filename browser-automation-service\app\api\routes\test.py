"""
Test endpoints for browser automation functionality
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel

from app.models.browser_automation import BrowserAutomationRequest, UserTier, RoutingStrategy
from app.services.session_manager import SessionManager
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


class TestRequest(BaseModel):
    """Test request model"""
    task: str
    user_tier: UserTier = UserTier.PRO
    enable_verification: bool = True
    max_steps: int = 10


class TestResponse(BaseModel):
    """Test response model"""
    success: bool
    message: str
    test_results: Dict[str, Any]


async def get_session_manager(request: Request) -> SessionManager:
    """Dependency to get session manager"""
    return request.app.state.session_manager


@router.post("/browser-automation", response_model=TestResponse)
async def test_browser_automation(
    test_request: TestRequest,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Test endpoint for browser automation functionality
    
    This endpoint allows testing the complete browser automation pipeline:
    - LangGraph orchestration
    - Browser Use integration
    - Role routing
    - Memory functionality
    - Google Search verification
    """
    
    logger.info(
        "Starting browser automation test",
        task=test_request.task,
        user_tier=test_request.user_tier
    )
    
    try:
        # Create test automation request
        automation_request = BrowserAutomationRequest(
            task=test_request.task,
            user_id="test_user_001",
            config_id="test_config_001",
            user_tier=test_request.user_tier,
            routing_strategy=RoutingStrategy.COMPLEX_ROUTING,
            enable_verification=test_request.enable_verification,
            max_steps=test_request.max_steps,
            timeout=120,  # 2 minutes for testing
            metadata={"test_mode": True}
        )
        
        # Import here to avoid circular imports
        from app.services.browser_orchestrator import BrowserOrchestrator
        
        # Create orchestrator in test mode
        orchestrator = BrowserOrchestrator(
            task_id="test_task_001",
            request=automation_request,
            session_manager=session_manager,
            test_mode=True
        )
        
        # Execute test
        result = await orchestrator.execute()
        
        # Prepare test results
        test_results = {
            "task_completed": result.success,
            "steps_executed": result.execution_metadata.steps_completed,
            "todo_items": len(result.todo_list),
            "completed_todos": len([t for t in result.todo_list if t.status == "completed"]),
            "verification_enabled": test_request.enable_verification,
            "verification_results": result.verification_results is not None,
            "tokens_used": result.execution_metadata.tokens_used,
            "execution_time_ms": (
                result.execution_metadata.end_time - result.execution_metadata.start_time
            ).total_seconds() * 1000 if result.execution_metadata.end_time else None,
            "screenshots_captured": len(result.screenshots),
            "extracted_data_keys": list(result.extracted_data.keys()) if result.extracted_data else [],
            "errors_encountered": len(result.execution_metadata.errors_encountered),
            "fallbacks_used": len(result.execution_metadata.fallbacks_used)
        }
        
        logger.info(
            "Browser automation test completed",
            success=result.success,
            steps_completed=result.execution_metadata.steps_completed
        )
        
        return TestResponse(
            success=result.success,
            message="Browser automation test completed successfully" if result.success else "Test completed with errors",
            test_results=test_results
        )
        
    except Exception as e:
        logger.error(
            "Browser automation test failed",
            error=str(e),
            exc_info=True
        )
        
        return TestResponse(
            success=False,
            message=f"Test failed: {str(e)}",
            test_results={"error": str(e)}
        )


@router.get("/health-detailed")
async def detailed_health_check(
    session_manager: SessionManager = Depends(get_session_manager)
):
    """Detailed health check for all browser automation components"""
    
    health_status = {
        "overall_status": "healthy",
        "components": {},
        "timestamp": "2025-01-21T00:00:00Z"
    }
    
    try:
        # Check session manager
        session_health = await session_manager.health_check()
        health_status["components"]["session_manager"] = {
            "status": "healthy" if session_health else "unhealthy",
            "details": session_health
        }
        
        # Check browser pool
        # TODO: Implement browser pool health check
        health_status["components"]["browser_pool"] = {
            "status": "healthy",
            "details": {"active_sessions": 0, "available_sessions": 10}
        }
        
        # Check LangGraph integration
        health_status["components"]["langgraph"] = {
            "status": "healthy",
            "details": {"workflows_available": True}
        }
        
        # Check Browser Use
        health_status["components"]["browser_use"] = {
            "status": "healthy",
            "details": {"playwright_available": True, "memory_enabled": True}
        }
        
        # Check Google Search API
        # TODO: Implement Google Search API health check
        health_status["components"]["google_search"] = {
            "status": "healthy",
            "details": {"api_key_configured": True}
        }
        
        # Determine overall status
        component_statuses = [comp["status"] for comp in health_status["components"].values()]
        if "unhealthy" in component_statuses:
            health_status["overall_status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        health_status["overall_status"] = "unhealthy"
        health_status["error"] = str(e)
        return health_status


@router.post("/simulate-task")
async def simulate_browser_task(task_description: str):
    """Simulate a browser automation task without actually executing it"""
    
    try:
        # Import here to avoid circular imports
        from app.services.task_planner import TaskPlanner
        
        # Create task planner
        planner = TaskPlanner()
        
        # Generate task plan
        plan = await planner.create_task_plan(task_description)
        
        return {
            "success": True,
            "task_description": task_description,
            "generated_plan": plan,
            "estimated_steps": len(plan.get("todo_list", [])),
            "estimated_complexity": plan.get("complexity", "medium"),
            "required_agents": plan.get("required_agents", []),
            "verification_needed": plan.get("verification_needed", True)
        }
        
    except Exception as e:
        logger.error(f"Task simulation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "task_description": task_description
        }
