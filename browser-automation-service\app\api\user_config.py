"""
User Configuration API
API endpoints for managing user browser automation configuration in custom model setup
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from app.core.logging import LoggerMixin
from app.services.user_config_manager import (
    user_config_manager, BrowserAutomationMode, AutomationTrigger
)
from app.services.access_control import access_control_manager


class BrowserAutomationConfigRequest(BaseModel):
    """Request model for browser automation configuration"""
    enabled: bool = Field(False, description="Enable browser automation")
    mode: str = Field("basic", description="Automation mode")
    trigger: str = Field("intent_based", description="Automation trigger type")
    
    # Feature toggles
    enable_navigation: bool = Field(True, description="Enable web navigation")
    enable_data_extraction: bool = Field(True, description="Enable data extraction")
    enable_form_interaction: bool = Field(False, description="Enable form interaction")
    enable_file_downloads: bool = Field(False, description="Enable file downloads")
    enable_screenshot_capture: bool = Field(True, description="Enable screenshot capture")
    enable_verification: bool = Field(True, description="Enable result verification")
    
    # Performance settings
    max_concurrent_sessions: int = Field(1, description="Maximum concurrent sessions")
    session_timeout_minutes: int = Field(10, description="Session timeout in minutes")
    enable_session_reuse: bool = Field(True, description="Enable session reuse")
    enable_performance_optimization: bool = Field(True, description="Enable performance optimization")
    
    # Security settings
    allow_external_sites: bool = Field(True, description="Allow external sites")
    blocked_domains: List[str] = Field(default_factory=list, description="Blocked domains")
    allowed_domains: List[str] = Field(default_factory=list, description="Allowed domains")
    enable_content_filtering: bool = Field(False, description="Enable content filtering")
    
    # Integration settings
    enable_role_routing: bool = Field(True, description="Enable role routing")
    preferred_roles: List[str] = Field(default_factory=list, description="Preferred roles")
    enable_fallback_strategies: bool = Field(True, description="Enable fallback strategies")
    enable_error_recovery: bool = Field(True, description="Enable error recovery")
    
    # Notification settings
    notify_on_completion: bool = Field(False, description="Notify on completion")
    notify_on_errors: bool = Field(True, description="Notify on errors")
    include_screenshots_in_results: bool = Field(False, description="Include screenshots in results")
    
    # Advanced settings
    custom_user_agent: Optional[str] = Field(None, description="Custom user agent")
    custom_viewport_size: Optional[str] = Field(None, description="Custom viewport size")
    enable_javascript: bool = Field(True, description="Enable JavaScript")
    enable_images: bool = Field(False, description="Enable images")
    enable_css: bool = Field(True, description="Enable CSS")


class UserConfigUpdateRequest(BaseModel):
    """Request model for updating user configuration"""
    config_name: str = Field("default", description="Configuration name")
    browser_automation: Optional[BrowserAutomationConfigRequest] = Field(None, description="Browser automation config")
    enable_browser_automation: Optional[bool] = Field(None, description="Enable browser automation")
    browser_automation_priority: Optional[int] = Field(None, description="Browser automation priority (1-10)")
    
    # Model configuration (existing RouKey functionality)
    model_configs: Optional[Dict[str, Any]] = Field(None, description="Model configurations")
    routing_strategies: Optional[Dict[str, Any]] = Field(None, description="Routing strategies")
    role_assignments: Optional[Dict[str, Any]] = Field(None, description="Role assignments")


class ToggleRequest(BaseModel):
    """Request model for toggling browser automation"""
    enabled: bool = Field(..., description="Enable/disable browser automation")
    config_name: str = Field("default", description="Configuration name")


class TemplateRequest(BaseModel):
    """Request model for applying configuration template"""
    template_name: str = Field(..., description="Template name to apply")
    config_name: str = Field("default", description="Configuration name")


class UserConfigAPI(LoggerMixin):
    """API endpoints for user configuration management"""
    
    def __init__(self):
        self.router = APIRouter(prefix="/api/user-config", tags=["user-config"])
        self._setup_routes()
        self.log_info("User config API initialized")
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.router.get("/ui-data/{user_id}")
        async def get_config_ui_data(user_id: str):
            """
            Get configuration UI data for custom model setup page
            
            Args:
                user_id: User identifier
                
            Returns:
                UI configuration data including browser automation settings
            """
            try:
                self.log_info(f"Getting config UI data for user: {user_id}")
                
                # Get UI data from config manager
                ui_data = await user_config_manager.get_config_ui_data(user_id)
                
                if "error" in ui_data:
                    raise HTTPException(status_code=500, detail=ui_data["error"])
                
                return {
                    "user_id": user_id,
                    "timestamp": datetime.now().isoformat(),
                    "config_data": ui_data
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to get config UI data: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to get configuration data: {e}"
                )
        
        @self.router.get("/config/{user_id}")
        async def get_user_config(
            user_id: str,
            config_name: str = Query("default", description="Configuration name")
        ):
            """
            Get user's complete configuration
            
            Args:
                user_id: User identifier
                config_name: Configuration name
                
            Returns:
                User configuration
            """
            try:
                self.log_info(f"Getting user config: {user_id}", config_name=config_name)
                
                # Get user configuration
                config = await user_config_manager.get_user_config(user_id, config_name)
                
                if not config:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Configuration not found: {config_name}"
                    )
                
                return {
                    "user_id": user_id,
                    "config_name": config_name,
                    "created_at": config.created_at.isoformat(),
                    "updated_at": config.updated_at.isoformat(),
                    "last_used": config.last_used.isoformat() if config.last_used else None,
                    "subscription_tier": config.subscription_tier,
                    "browser_automation": {
                        "enabled": config.enable_browser_automation,
                        "mode": config.browser_automation.mode.value,
                        "trigger": config.browser_automation.trigger.value,
                        "features": {
                            "navigation": config.browser_automation.enable_navigation,
                            "data_extraction": config.browser_automation.enable_data_extraction,
                            "form_interaction": config.browser_automation.enable_form_interaction,
                            "file_downloads": config.browser_automation.enable_file_downloads,
                            "screenshot_capture": config.browser_automation.enable_screenshot_capture,
                            "verification": config.browser_automation.enable_verification
                        },
                        "performance": {
                            "max_concurrent_sessions": config.browser_automation.max_concurrent_sessions,
                            "session_timeout_minutes": config.browser_automation.session_timeout_minutes,
                            "enable_session_reuse": config.browser_automation.enable_session_reuse,
                            "enable_performance_optimization": config.browser_automation.enable_performance_optimization
                        },
                        "security": {
                            "allow_external_sites": config.browser_automation.allow_external_sites,
                            "blocked_domains": config.browser_automation.blocked_domains,
                            "allowed_domains": config.browser_automation.allowed_domains,
                            "enable_content_filtering": config.browser_automation.enable_content_filtering
                        },
                        "integration": {
                            "enable_role_routing": config.browser_automation.enable_role_routing,
                            "preferred_roles": config.browser_automation.preferred_roles,
                            "enable_fallback_strategies": config.browser_automation.enable_fallback_strategies,
                            "enable_error_recovery": config.browser_automation.enable_error_recovery
                        }
                    },
                    "model_configs": config.model_configs,
                    "routing_strategies": config.routing_strategies,
                    "role_assignments": config.role_assignments
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to get user config: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to get user configuration: {e}"
                )
        
        @self.router.put("/config/{user_id}")
        async def update_user_config(
            user_id: str,
            request: UserConfigUpdateRequest
        ):
            """
            Update user's configuration
            
            Args:
                user_id: User identifier
                request: Configuration update request
                
            Returns:
                Update result
            """
            try:
                self.log_info(f"Updating user config: {user_id}", config_name=request.config_name)
                
                # Prepare config updates
                config_updates = {}
                
                if request.browser_automation:
                    config_updates["browser_automation"] = request.browser_automation.dict(exclude_unset=True)
                
                if request.enable_browser_automation is not None:
                    config_updates["enable_browser_automation"] = request.enable_browser_automation
                
                if request.browser_automation_priority is not None:
                    config_updates["browser_automation_priority"] = request.browser_automation_priority
                
                if request.model_configs:
                    config_updates["model_configs"] = request.model_configs
                
                if request.routing_strategies:
                    config_updates["routing_strategies"] = request.routing_strategies
                
                if request.role_assignments:
                    config_updates["role_assignments"] = request.role_assignments
                
                # Update configuration
                success = await user_config_manager.update_user_config(
                    user_id=user_id,
                    config_updates=config_updates,
                    config_name=request.config_name
                )
                
                if not success:
                    raise HTTPException(
                        status_code=400,
                        detail="Configuration update failed - check tier restrictions"
                    )
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "config_name": request.config_name,
                    "updated_at": datetime.now().isoformat(),
                    "message": "Configuration updated successfully"
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to update user config: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to update configuration: {e}"
                )
        
        @self.router.post("/toggle-automation/{user_id}")
        async def toggle_browser_automation(
            user_id: str,
            request: ToggleRequest
        ):
            """
            Toggle browser automation for user
            
            Args:
                user_id: User identifier
                request: Toggle request
                
            Returns:
                Toggle result with status and available features
            """
            try:
                self.log_info(f"Toggling browser automation: {user_id}", enabled=request.enabled)
                
                # Toggle browser automation
                result = await user_config_manager.toggle_browser_automation(
                    user_id=user_id,
                    enabled=request.enabled,
                    config_name=request.config_name
                )
                
                if not result.get("success", False):
                    error_message = result.get("error", "Toggle failed")
                    required_tier = result.get("required_tier")
                    
                    if required_tier:
                        raise HTTPException(
                            status_code=403,
                            detail={
                                "message": error_message,
                                "required_tier": required_tier,
                                "upgrade_required": True
                            }
                        )
                    else:
                        raise HTTPException(status_code=400, detail=error_message)
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "config_name": request.config_name,
                    "browser_automation": {
                        "enabled": result["enabled"],
                        "mode": result["mode"],
                        "available_features": result["available_features"],
                        "usage_info": result["usage_info"]
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to toggle browser automation: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to toggle browser automation: {e}"
                )
        
        @self.router.post("/apply-template/{user_id}")
        async def apply_configuration_template(
            user_id: str,
            request: TemplateRequest
        ):
            """
            Apply a configuration template to user's settings
            
            Args:
                user_id: User identifier
                request: Template application request
                
            Returns:
                Template application result
            """
            try:
                self.log_info(f"Applying template: {user_id}", template=request.template_name)
                
                # Apply template
                result = await user_config_manager.apply_template(
                    user_id=user_id,
                    template_name=request.template_name,
                    config_name=request.config_name
                )
                
                if not result.get("success", False):
                    error_message = result.get("error", "Template application failed")
                    raise HTTPException(status_code=400, detail=error_message)
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "config_name": request.config_name,
                    "template_applied": result["template_applied"],
                    "configuration": result["configuration"],
                    "timestamp": datetime.now().isoformat()
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to apply template: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to apply template: {e}"
                )
        
        @self.router.get("/templates/{user_id}")
        async def get_available_templates(user_id: str):
            """
            Get available configuration templates for user
            
            Args:
                user_id: User identifier
                
            Returns:
                Available templates based on user's subscription tier
            """
            try:
                self.log_info(f"Getting available templates: {user_id}")
                
                # Get UI data which includes available templates
                ui_data = await user_config_manager.get_config_ui_data(user_id)
                
                if "error" in ui_data:
                    raise HTTPException(status_code=500, detail=ui_data["error"])
                
                templates = ui_data.get("browser_automation", {}).get("templates", {})
                
                return {
                    "user_id": user_id,
                    "available_templates": templates,
                    "timestamp": datetime.now().isoformat()
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to get available templates: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to get available templates: {e}"
                )
        
        @self.router.get("/usage/{user_id}")
        async def get_usage_info(user_id: str):
            """
            Get browser automation usage information for user
            
            Args:
                user_id: User identifier
                
            Returns:
                Usage information and quota status
            """
            try:
                self.log_info(f"Getting usage info: {user_id}")
                
                # Get quota status from access control manager
                quota_status = await access_control_manager.check_browsing_quota(user_id)
                
                # Get user limits
                user_limits = await access_control_manager.get_user_limits(user_id)
                
                return {
                    "user_id": user_id,
                    "subscription_tier": user_limits.get("subscription_tier", "starter"),
                    "quota_status": quota_status,
                    "limits": user_limits,
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                self.log_error(f"Failed to get usage info: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to get usage information: {e}"
                )


# Create API instance
user_config_api = UserConfigAPI()
router = user_config_api.router
