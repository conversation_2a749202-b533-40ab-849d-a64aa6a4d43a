"""
Configuration settings for the Browser Automation Service
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Service Configuration
    SERVICE_HOST: str = Field(default="localhost", env="SERVICE_HOST")
    SERVICE_PORT: int = Field(default=8000, env="SERVICE_PORT")
    SERVICE_ENV: str = Field(default="development", env="SERVICE_ENV")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # RouKey Integration
    ROKEY_API_URL: str = Field(default="http://localhost:3000", env="ROKEY_API_URL")
    ROKEY_API_SECRET: str = Field(default="", env="ROKEY_API_SECRET")
    
    # Database Configuration
    DATABASE_URL: str = Field(default="", env="DATABASE_URL")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_SESSION_DB: int = Field(default=1, env="REDIS_SESSION_DB")
    REDIS_CACHE_DB: int = Field(default=2, env="REDIS_CACHE_DB")
    
    # LLM API Keys
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    GOOGLE_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    DEEPSEEK_API_KEY: Optional[str] = Field(default=None, env="DEEPSEEK_API_KEY")
    GROK_API_KEY: Optional[str] = Field(default=None, env="GROK_API_KEY")
    
    # Google Custom Search API
    GOOGLE_SEARCH_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_SEARCH_API_KEY")
    GOOGLE_SEARCH_ENGINE_ID: Optional[str] = Field(default=None, env="GOOGLE_SEARCH_ENGINE_ID")
    
    # Memory Configuration (Qdrant)
    QDRANT_HOST: str = Field(default="localhost", env="QDRANT_HOST")
    QDRANT_PORT: int = Field(default=6333, env="QDRANT_PORT")
    QDRANT_API_KEY: Optional[str] = Field(default=None, env="QDRANT_API_KEY")
    
    # Browser Configuration
    BROWSER_HEADLESS: bool = Field(default=True, env="BROWSER_HEADLESS")
    BROWSER_TIMEOUT: int = Field(default=30000, env="BROWSER_TIMEOUT")
    BROWSER_VIEWPORT_WIDTH: int = Field(default=1920, env="BROWSER_VIEWPORT_WIDTH")
    BROWSER_VIEWPORT_HEIGHT: int = Field(default=1080, env="BROWSER_VIEWPORT_HEIGHT")
    
    # Session Management
    SESSION_POOL_SIZE: int = Field(default=10, env="SESSION_POOL_SIZE")
    SESSION_TIMEOUT: int = Field(default=300, env="SESSION_TIMEOUT")
    MAX_CONCURRENT_SESSIONS: int = Field(default=5, env="MAX_CONCURRENT_SESSIONS")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    
    # Security
    JWT_SECRET_KEY: str = Field(default="your-secret-key", env="JWT_SECRET_KEY")
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000"],
        env="CORS_ORIGINS"
    )
    
    # Performance
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    REQUEST_TIMEOUT: int = Field(default=300, env="REQUEST_TIMEOUT")
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Parse CORS_ORIGINS if it's a string
        if isinstance(self.CORS_ORIGINS, str):
            self.CORS_ORIGINS = [origin.strip() for origin in self.CORS_ORIGINS.split(",")]


# Global settings instance
settings = Settings()
