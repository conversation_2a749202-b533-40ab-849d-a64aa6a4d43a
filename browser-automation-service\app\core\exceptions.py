"""
Exception handling for the Browser Automation Service
"""

from typing import Any, Dict
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging

logger = logging.getLogger(__name__)


class BrowserAutomationException(Exception):
    """Base exception for browser automation errors"""
    
    def __init__(self, message: str, error_code: str = "BROWSER_ERROR", details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class SessionException(BrowserAutomationException):
    """Exception for session management errors"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "SESSION_ERROR", details)


class LangGraphException(BrowserAutomationException):
    """Exception for LangGraph workflow errors"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "LANGGRAPH_ERROR", details)


class BrowserUseException(BrowserAutomationException):
    """Exception for Browser Use errors"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "BROWSER_USE_ERROR", details)


class RouKeyIntegrationException(BrowserAutomationException):
    """Exception for RouKey integration errors"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "ROKEY_INTEGRATION_ERROR", details)


class TierLimitException(BrowserAutomationException):
    """Exception for tier limit violations"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "TIER_LIMIT_ERROR", details)


async def browser_automation_exception_handler(request: Request, exc: BrowserAutomationException):
    """Handle browser automation exceptions"""
    logger.error(
        f"Browser automation error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    return JSONResponse(
        status_code=400,
        content={
            "error": True,
            "error_code": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
    )


async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions"""
    logger.warning(
        f"HTTP error {exc.status_code}: {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "error_code": f"HTTP_{exc.status_code}",
            "message": exc.detail
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation exceptions"""
    logger.warning(
        f"Validation error: {exc.errors()}",
        extra={
            "path": request.url.path,
            "method": request.method,
            "errors": exc.errors()
        }
    )
    
    return JSONResponse(
        status_code=422,
        content={
            "error": True,
            "error_code": "VALIDATION_ERROR",
            "message": "Request validation failed",
            "details": exc.errors()
        }
    )


async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(
        f"Unexpected error: {str(exc)}",
        extra={
            "path": request.url.path,
            "method": request.method,
            "exception_type": type(exc).__name__
        },
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "error_code": "INTERNAL_ERROR",
            "message": "An unexpected error occurred"
        }
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup exception handlers for the FastAPI app"""
    
    app.add_exception_handler(BrowserAutomationException, browser_automation_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
