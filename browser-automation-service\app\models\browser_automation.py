"""
Pydantic models for browser automation requests and responses
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WorkflowType(str, Enum):
    """Workflow type enumeration"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    SUPERVISOR = "supervisor"
    HIERARCHICAL = "hierarchical"


class RoutingStrategy(str, Enum):
    """RouKey routing strategy enumeration"""
    COMPLEX_ROUTING = "complex_routing"
    STRICT_FALLBACK = "strict_fallback"
    AB_TESTING = "ab_testing"
    COST_OPTIMIZED = "cost_optimized"


class UserTier(str, Enum):
    """User subscription tier enumeration"""
    FREE = "free"
    STARTER = "starter"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class TodoItem(BaseModel):
    """Individual todo item in the task plan"""
    id: str = Field(..., description="Unique identifier for the todo item")
    task: str = Field(..., description="Description of the task to be performed")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Current status of the task")
    assigned_agent: Optional[str] = Field(None, description="Agent assigned to this task")
    priority: int = Field(default=1, description="Priority level (1-10, higher is more important)")
    dependencies: List[str] = Field(default_factory=list, description="List of task IDs this task depends on")
    completion_time: Optional[datetime] = Field(None, description="When the task was completed")
    result: Optional[Dict[str, Any]] = Field(None, description="Result data from task execution")
    error_message: Optional[str] = Field(None, description="Error message if task failed")


class BrowserSession(BaseModel):
    """Browser session information"""
    session_id: str = Field(..., description="Unique session identifier")
    current_url: Optional[str] = Field(None, description="Current URL in the browser")
    tabs: List[Dict[str, Any]] = Field(default_factory=list, description="Open tabs information")
    cookies: Dict[str, Any] = Field(default_factory=dict, description="Session cookies")
    local_storage: Dict[str, Any] = Field(default_factory=dict, description="Local storage data")
    memory_context: Dict[str, Any] = Field(default_factory=dict, description="Browser Use memory context")
    created_at: datetime = Field(default_factory=datetime.now, description="Session creation time")
    last_activity: datetime = Field(default_factory=datetime.now, description="Last activity timestamp")


class VerificationResult(BaseModel):
    """Result from verification agent"""
    google_search_results: List[Dict[str, Any]] = Field(default_factory=list, description="Google search results")
    credibility_scores: Dict[str, float] = Field(default_factory=dict, description="Source credibility scores")
    cross_references: List[Dict[str, Any]] = Field(default_factory=list, description="Cross-reference data")
    verification_status: str = Field(..., description="Overall verification status")
    confidence_score: float = Field(..., description="Confidence in the verification (0-1)")


class ExecutionMetadata(BaseModel):
    """Metadata about task execution"""
    start_time: datetime = Field(default_factory=datetime.now, description="Execution start time")
    end_time: Optional[datetime] = Field(None, description="Execution end time")
    steps_completed: int = Field(default=0, description="Number of steps completed")
    errors_encountered: List[Dict[str, Any]] = Field(default_factory=list, description="Errors during execution")
    fallbacks_used: List[Dict[str, Any]] = Field(default_factory=list, description="Fallback strategies used")
    cost_tracking: Dict[str, Any] = Field(default_factory=dict, description="Cost tracking information")
    tokens_used: int = Field(default=0, description="Total tokens consumed")
    llm_calls: int = Field(default=0, description="Number of LLM API calls made")


class BrowserAutomationRequest(BaseModel):
    """Request for browser automation task"""
    task: str = Field(..., description="The task to be performed by the browser automation system")
    user_id: str = Field(..., description="User identifier")
    config_id: str = Field(..., description="Custom configuration ID")
    user_tier: UserTier = Field(..., description="User subscription tier")
    routing_strategy: RoutingStrategy = Field(default=RoutingStrategy.COMPLEX_ROUTING, description="Routing strategy to use")
    workflow_type: Optional[WorkflowType] = Field(None, description="Preferred workflow type (auto-determined if not specified)")
    enable_verification: bool = Field(default=True, description="Enable Google Search verification")
    enable_memory: bool = Field(default=True, description="Enable Browser Use memory functionality")
    max_steps: int = Field(default=50, description="Maximum number of steps to execute")
    timeout: int = Field(default=300, description="Timeout in seconds")
    priority: int = Field(default=1, description="Task priority (1-10)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('max_steps')
    def validate_max_steps(cls, v):
        if v < 1 or v > 100:
            raise ValueError('max_steps must be between 1 and 100')
        return v
    
    @validator('timeout')
    def validate_timeout(cls, v):
        if v < 10 or v > 600:
            raise ValueError('timeout must be between 10 and 600 seconds')
        return v


class BrowserAutomationState(BaseModel):
    """Complete state of browser automation workflow"""
    main_task: str = Field(..., description="Original user request")
    user_config: Dict[str, Any] = Field(..., description="RouKey user configuration")
    role_assignments: Dict[str, Any] = Field(default_factory=dict, description="LLM assignments per agent")
    routing_strategy: RoutingStrategy = Field(..., description="Active routing strategy")
    tier_limits: Dict[str, Any] = Field(default_factory=dict, description="User subscription limits")
    
    todo_list: List[TodoItem] = Field(default_factory=list, description="Task breakdown and progress")
    browser_session: Optional[BrowserSession] = Field(None, description="Browser session information")
    verification_results: Optional[VerificationResult] = Field(None, description="Verification results")
    execution_metadata: ExecutionMetadata = Field(default_factory=ExecutionMetadata, description="Execution metadata")
    
    workflow_type: WorkflowType = Field(..., description="Active workflow type")
    current_agent: Optional[str] = Field(None, description="Currently active agent")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Overall task status")


class BrowserAutomationResponse(BaseModel):
    """Response from browser automation task"""
    success: bool = Field(..., description="Whether the task completed successfully")
    task_id: str = Field(..., description="Unique task identifier")
    final_result: str = Field(..., description="Final result of the automation task")
    todo_list: List[TodoItem] = Field(..., description="Complete todo list with status")
    verification_results: Optional[VerificationResult] = Field(None, description="Verification results if enabled")
    execution_metadata: ExecutionMetadata = Field(..., description="Execution metadata and statistics")
    screenshots: List[str] = Field(default_factory=list, description="URLs to captured screenshots")
    extracted_data: Dict[str, Any] = Field(default_factory=dict, description="Extracted data from web pages")
    error_message: Optional[str] = Field(None, description="Error message if task failed")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
