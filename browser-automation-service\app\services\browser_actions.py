"""
Browser Actions Handler
Comprehensive browser action implementations for RouKey automation
"""

import asyncio
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from playwright.async_api import Page, Locator, ElementHandle
from browser_use import ActionResult

from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserUseException


class BrowserActions(LoggerMixin):
    """
    Comprehensive browser actions for RouKey automation
    
    Provides all Browser Use capabilities:
    - Navigation and page management
    - Element interaction (clicking, typing, scrolling)
    - Form handling and input management
    - Data extraction and content analysis
    - Calendar and date picker interactions
    - File upload/download operations
    - Screenshot and visual verification
    - Multi-tab and window management
    """
    
    def __init__(self, page: Page):
        self.page = page
        self.action_history: List[Dict[str, Any]] = []
    
    async def navigate_and_wait(
        self,
        url: str,
        wait_for: str = "networkidle",
        timeout: int = 30000
    ) -> ActionResult:
        """Navigate to URL and wait for page to load"""
        try:
            self.log_info(f"Navigating to: {url}")
            
            # Navigate to URL
            response = await self.page.goto(url, wait_until=wait_for, timeout=timeout)
            
            # Wait for page to be fully loaded
            await self.page.wait_for_load_state("domcontentloaded")
            
            # Record action
            await self._record_action("navigate", {
                "url": url,
                "status_code": response.status if response else None,
                "final_url": self.page.url
            })
            
            return ActionResult(
                extracted_content=f"Successfully navigated to {url}. Page title: {await self.page.title()}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Navigation failed: {e}")
            return ActionResult(
                extracted_content=f"Navigation to {url} failed: {e}",
                include_in_memory=False
            )
    
    async def click_element(
        self,
        selector: str,
        wait_timeout: int = 10000,
        force: bool = False
    ) -> ActionResult:
        """Click on an element using CSS selector or text"""
        try:
            self.log_info(f"Clicking element: {selector}")
            
            # Wait for element to be visible and clickable
            element = await self.page.wait_for_selector(
                selector, 
                timeout=wait_timeout,
                state="visible"
            )
            
            if not element:
                return ActionResult(
                    extracted_content=f"Element not found: {selector}",
                    include_in_memory=False
                )
            
            # Scroll element into view
            await element.scroll_into_view_if_needed()
            
            # Click the element
            await element.click(force=force)
            
            # Wait for any navigation or changes
            await asyncio.sleep(1)
            
            await self._record_action("click", {
                "selector": selector,
                "element_text": await element.text_content(),
                "current_url": self.page.url
            })
            
            return ActionResult(
                extracted_content=f"Successfully clicked element: {selector}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Click failed: {e}")
            return ActionResult(
                extracted_content=f"Failed to click {selector}: {e}",
                include_in_memory=False
            )
    
    async def type_text(
        self,
        selector: str,
        text: str,
        clear_first: bool = True,
        delay: int = 50
    ) -> ActionResult:
        """Type text into an input field"""
        try:
            self.log_info(f"Typing text into: {selector}")
            
            # Wait for input field
            element = await self.page.wait_for_selector(selector, timeout=10000)
            
            if not element:
                return ActionResult(
                    extracted_content=f"Input field not found: {selector}",
                    include_in_memory=False
                )
            
            # Focus on the element
            await element.focus()
            
            # Clear existing text if requested
            if clear_first:
                await element.fill("")
            
            # Type the text with delay
            await element.type(text, delay=delay)
            
            await self._record_action("type", {
                "selector": selector,
                "text": text[:50] + "..." if len(text) > 50 else text,
                "field_type": await element.get_attribute("type")
            })
            
            return ActionResult(
                extracted_content=f"Successfully typed text into {selector}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Typing failed: {e}")
            return ActionResult(
                extracted_content=f"Failed to type into {selector}: {e}",
                include_in_memory=False
            )
    
    async def scroll_page(
        self,
        direction: str = "down",
        amount: int = 500,
        smooth: bool = True
    ) -> ActionResult:
        """Scroll the page in specified direction"""
        try:
            self.log_info(f"Scrolling {direction} by {amount}px")
            
            # Calculate scroll coordinates
            if direction.lower() == "down":
                delta_y = amount
            elif direction.lower() == "up":
                delta_y = -amount
            else:
                delta_y = 0
            
            # Perform scroll
            if smooth:
                await self.page.evaluate(f"""
                    window.scrollBy({{
                        top: {delta_y},
                        left: 0,
                        behavior: 'smooth'
                    }});
                """)
            else:
                await self.page.evaluate(f"window.scrollBy(0, {delta_y})")
            
            # Wait for scroll to complete
            await asyncio.sleep(1)
            
            await self._record_action("scroll", {
                "direction": direction,
                "amount": amount,
                "current_scroll": await self.page.evaluate("window.pageYOffset")
            })
            
            return ActionResult(
                extracted_content=f"Scrolled {direction} by {amount}px",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Scrolling failed: {e}")
            return ActionResult(
                extracted_content=f"Scrolling failed: {e}",
                include_in_memory=False
            )
    
    async def extract_text_content(
        self,
        selector: str = "body",
        max_length: int = 5000
    ) -> ActionResult:
        """Extract text content from page or specific element"""
        try:
            self.log_info(f"Extracting text content from: {selector}")
            
            # Get element or default to body
            if selector == "body":
                text_content = await self.page.text_content("body")
            else:
                element = await self.page.query_selector(selector)
                if element:
                    text_content = await element.text_content()
                else:
                    return ActionResult(
                        extracted_content=f"Element not found for text extraction: {selector}",
                        include_in_memory=False
                    )
            
            # Clean and truncate text
            if text_content:
                # Remove extra whitespace
                cleaned_text = re.sub(r'\s+', ' ', text_content.strip())
                
                # Truncate if too long
                if len(cleaned_text) > max_length:
                    cleaned_text = cleaned_text[:max_length] + "..."
                
                await self._record_action("extract_text", {
                    "selector": selector,
                    "text_length": len(cleaned_text),
                    "preview": cleaned_text[:100] + "..." if len(cleaned_text) > 100 else cleaned_text
                })
                
                return ActionResult(
                    extracted_content=cleaned_text,
                    include_in_memory=True
                )
            else:
                return ActionResult(
                    extracted_content="No text content found",
                    include_in_memory=False
                )
                
        except Exception as e:
            self.log_error(f"Text extraction failed: {e}")
            return ActionResult(
                extracted_content=f"Text extraction failed: {e}",
                include_in_memory=False
            )
    
    async def extract_links(
        self,
        filter_pattern: str = None,
        limit: int = 20
    ) -> ActionResult:
        """Extract links from the current page"""
        try:
            self.log_info("Extracting links from page")
            
            # Get all links
            links = await self.page.query_selector_all("a[href]")
            
            extracted_links = []
            for link in links[:limit]:
                href = await link.get_attribute("href")
                text = await link.text_content()
                
                if href:
                    # Apply filter if provided
                    if filter_pattern and not re.search(filter_pattern, href, re.IGNORECASE):
                        continue
                    
                    # Convert relative URLs to absolute
                    if href.startswith("/"):
                        href = f"{self.page.url.split('/')[0]}//{self.page.url.split('/')[2]}{href}"
                    elif not href.startswith("http"):
                        base_url = "/".join(self.page.url.split("/")[:-1])
                        href = f"{base_url}/{href}"
                    
                    extracted_links.append({
                        "url": href,
                        "text": text.strip() if text else "",
                        "domain": href.split("/")[2] if href.startswith("http") else ""
                    })
            
            await self._record_action("extract_links", {
                "total_links": len(extracted_links),
                "filter_pattern": filter_pattern,
                "page_url": self.page.url
            })
            
            links_text = "\n".join([f"- {link['text']}: {link['url']}" for link in extracted_links])
            
            return ActionResult(
                extracted_content=f"Extracted {len(extracted_links)} links:\n{links_text}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Link extraction failed: {e}")
            return ActionResult(
                extracted_content=f"Link extraction failed: {e}",
                include_in_memory=False
            )
    
    async def fill_form(
        self,
        form_data: Dict[str, str],
        submit_button: str = None
    ) -> ActionResult:
        """Fill out a form with provided data"""
        try:
            self.log_info("Filling form with provided data")
            
            filled_fields = []
            
            for field_selector, value in form_data.items():
                try:
                    # Wait for field to be available
                    field = await self.page.wait_for_selector(field_selector, timeout=5000)
                    
                    if field:
                        # Get field type
                        field_type = await field.get_attribute("type") or "text"
                        tag_name = await field.evaluate("el => el.tagName.toLowerCase()")
                        
                        # Handle different field types
                        if tag_name == "select":
                            await field.select_option(value)
                        elif field_type in ["checkbox", "radio"]:
                            if value.lower() in ["true", "1", "yes", "on"]:
                                await field.check()
                            else:
                                await field.uncheck()
                        else:
                            await field.fill(value)
                        
                        filled_fields.append(field_selector)
                        
                except Exception as field_error:
                    self.log_warning(f"Failed to fill field {field_selector}: {field_error}")
                    continue
            
            # Submit form if button provided
            if submit_button:
                try:
                    submit_element = await self.page.wait_for_selector(submit_button, timeout=5000)
                    if submit_element:
                        await submit_element.click()
                        await asyncio.sleep(2)  # Wait for submission
                except Exception as submit_error:
                    self.log_warning(f"Failed to submit form: {submit_error}")
            
            await self._record_action("fill_form", {
                "fields_filled": filled_fields,
                "total_fields": len(form_data),
                "submitted": submit_button is not None
            })
            
            return ActionResult(
                extracted_content=f"Successfully filled {len(filled_fields)} out of {len(form_data)} form fields",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Form filling failed: {e}")
            return ActionResult(
                extracted_content=f"Form filling failed: {e}",
                include_in_memory=False
            )
    
    async def handle_calendar_picker(
        self,
        date_selector: str,
        target_date: str,
        date_format: str = "%Y-%m-%d"
    ) -> ActionResult:
        """Handle calendar/date picker interactions"""
        try:
            self.log_info(f"Handling calendar picker for date: {target_date}")
            
            # Parse target date
            target_dt = datetime.strptime(target_date, date_format)
            
            # Click on date picker to open it
            date_field = await self.page.wait_for_selector(date_selector, timeout=10000)
            await date_field.click()
            
            # Wait for calendar to appear
            await asyncio.sleep(1)
            
            # Try different calendar selectors
            calendar_selectors = [
                ".calendar",
                ".datepicker",
                "[role='dialog']",
                ".react-datepicker",
                ".ui-datepicker"
            ]
            
            calendar_found = False
            for selector in calendar_selectors:
                calendar = await self.page.query_selector(selector)
                if calendar:
                    calendar_found = True
                    break
            
            if not calendar_found:
                # Fallback: try to type date directly
                await date_field.fill(target_date)
                
                await self._record_action("calendar_picker", {
                    "target_date": target_date,
                    "method": "direct_input",
                    "success": True
                })
                
                return ActionResult(
                    extracted_content=f"Set date to {target_date} using direct input",
                    include_in_memory=True
                )
            
            # Try to navigate to correct month/year
            current_month = target_dt.month
            current_year = target_dt.year
            
            # Look for month/year navigation buttons
            month_nav = await self.page.query_selector_all("[class*='month'], [class*='nav']")
            year_nav = await self.page.query_selector_all("[class*='year'], [class*='nav']")
            
            # Click on the target day
            day_selectors = [
                f"[data-date*='{target_date}']",
                f"[aria-label*='{target_dt.day}']",
                f".day:has-text('{target_dt.day}')",
                f"td:has-text('{target_dt.day}')"
            ]
            
            day_clicked = False
            for day_selector in day_selectors:
                try:
                    day_element = await self.page.query_selector(day_selector)
                    if day_element:
                        await day_element.click()
                        day_clicked = True
                        break
                except Exception:
                    continue
            
            if not day_clicked:
                # Fallback to direct input
                await date_field.fill(target_date)
            
            await self._record_action("calendar_picker", {
                "target_date": target_date,
                "method": "calendar_interaction",
                "day_clicked": day_clicked
            })
            
            return ActionResult(
                extracted_content=f"Successfully set date to {target_date}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Calendar picker failed: {e}")
            return ActionResult(
                extracted_content=f"Calendar picker failed: {e}",
                include_in_memory=False
            )
    
    async def take_screenshot(
        self,
        filename: str = None,
        full_page: bool = True,
        element_selector: str = None
    ) -> ActionResult:
        """Take a screenshot of the page or specific element"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = filename or f"screenshot_{timestamp}.png"
            
            if element_selector:
                # Screenshot specific element
                element = await self.page.query_selector(element_selector)
                if element:
                    await element.screenshot(path=filename)
                    screenshot_type = "element"
                else:
                    return ActionResult(
                        extracted_content=f"Element not found for screenshot: {element_selector}",
                        include_in_memory=False
                    )
            else:
                # Screenshot full page or viewport
                await self.page.screenshot(path=filename, full_page=full_page)
                screenshot_type = "full_page" if full_page else "viewport"
            
            await self._record_action("screenshot", {
                "filename": filename,
                "type": screenshot_type,
                "element_selector": element_selector,
                "page_url": self.page.url
            })
            
            return ActionResult(
                extracted_content=f"Screenshot saved as {filename}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Screenshot failed: {e}")
            return ActionResult(
                extracted_content=f"Screenshot failed: {e}",
                include_in_memory=False
            )
    
    async def wait_for_element(
        self,
        selector: str,
        timeout: int = 10000,
        state: str = "visible"
    ) -> ActionResult:
        """Wait for an element to appear with specified state"""
        try:
            self.log_info(f"Waiting for element: {selector}")
            
            element = await self.page.wait_for_selector(
                selector,
                timeout=timeout,
                state=state
            )
            
            if element:
                element_text = await element.text_content()
                
                await self._record_action("wait_for_element", {
                    "selector": selector,
                    "state": state,
                    "found": True,
                    "element_text": element_text[:100] if element_text else ""
                })
                
                return ActionResult(
                    extracted_content=f"Element found: {selector}",
                    include_in_memory=True
                )
            else:
                return ActionResult(
                    extracted_content=f"Element not found within timeout: {selector}",
                    include_in_memory=False
                )
                
        except Exception as e:
            self.log_error(f"Wait for element failed: {e}")
            return ActionResult(
                extracted_content=f"Wait for element failed: {e}",
                include_in_memory=False
            )
    
    async def get_page_info(self) -> ActionResult:
        """Get comprehensive information about the current page"""
        try:
            page_info = {
                "url": self.page.url,
                "title": await self.page.title(),
                "viewport": await self.page.viewport_size(),
                "user_agent": await self.page.evaluate("navigator.userAgent"),
                "timestamp": datetime.now().isoformat()
            }
            
            # Get page metrics if available
            try:
                metrics = await self.page.evaluate("""
                    () => ({
                        loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                        domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
                        firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
                    })
                """)
                page_info["performance"] = metrics
            except Exception:
                pass
            
            await self._record_action("get_page_info", page_info)
            
            info_text = f"""Page Information:
URL: {page_info['url']}
Title: {page_info['title']}
Viewport: {page_info['viewport']['width']}x{page_info['viewport']['height']}"""
            
            return ActionResult(
                extracted_content=info_text,
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Get page info failed: {e}")
            return ActionResult(
                extracted_content=f"Get page info failed: {e}",
                include_in_memory=False
            )
    
    async def _record_action(self, action_type: str, details: Dict[str, Any]):
        """Record action in history for debugging and analysis"""
        action_record = {
            "type": action_type,
            "timestamp": datetime.now().isoformat(),
            "details": details,
            "page_url": self.page.url,
            "page_title": await self.page.title()
        }
        
        self.action_history.append(action_record)
        
        # Keep only last 50 actions to prevent memory issues
        if len(self.action_history) > 50:
            self.action_history = self.action_history[-50:]
    
    def get_action_history(self) -> List[Dict[str, Any]]:
        """Get the action history for this session"""
        return self.action_history.copy()
