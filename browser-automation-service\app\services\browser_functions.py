"""
Custom Browser Functions for RouKey Integration
Extends Browser Use with RouKey-specific functionality
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from browser_use import Controller, ActionResult
from playwright.async_api import Page
from googleapiclient.discovery import build
import json

from app.core.config import settings
from app.core.logging import LoggerMixin


class BrowserFunctions(LoggerMixin):
    """Custom browser functions for RouKey browser automation"""
    
    def __init__(self):
        self.google_search_service = None
        if settings.GOOGLE_SEARCH_API_KEY and settings.GOOGLE_SEARCH_ENGINE_ID:
            self.google_search_service = build(
                "customsearch", 
                "v1", 
                developerKey=settings.GOOGLE_SEARCH_API_KEY
            )
    
    def register_functions(self, controller: Controller):
        """Register all custom functions with the Browser Use controller"""
        
        # Task management functions
        controller.action("Update todo list with task progress")(self.update_todo_progress)
        controller.action("Mark task as completed")(self.mark_task_completed)
        controller.action("Add new subtask to todo list")(self.add_subtask)
        
        # Google Search verification functions
        controller.action("Verify information with Google Search")(self.verify_with_google_search)
        controller.action("Cross-reference data with web search")(self.cross_reference_data)
        
        # Data extraction and formatting functions
        controller.action("Extract structured data from page")(self.extract_structured_data)
        controller.action("Format results for RouKey response")(self.format_rokey_response)
        
        # Session and state management
        controller.action("Save session state")(self.save_session_state)
        controller.action("Load session state")(self.load_session_state)
        
        # Error handling and recovery
        controller.action("Handle browser error and retry")(self.handle_browser_error)
        controller.action("Report task progress to RouKey")(self.report_progress)
        
        self.log_info("Custom browser functions registered")
    
    async def update_todo_progress(
        self, 
        task_id: str, 
        status: str, 
        result: str = None,
        page: Page = None
    ) -> ActionResult:
        """Update progress of a specific todo item"""
        try:
            # This would update the todo list in the LangGraph state
            progress_data = {
                "task_id": task_id,
                "status": status,
                "result": result,
                "timestamp": datetime.now().isoformat(),
                "current_url": page.url if page else None
            }
            
            self.log_info(f"Todo progress updated: {task_id} -> {status}")
            
            return ActionResult(
                extracted_content=f"Updated task {task_id} status to {status}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Failed to update todo progress: {e}")
            return ActionResult(
                extracted_content=f"Error updating todo progress: {e}",
                include_in_memory=False
            )
    
    async def mark_task_completed(
        self, 
        task_id: str, 
        final_result: str,
        page: Page = None
    ) -> ActionResult:
        """Mark a task as completed with final result"""
        try:
            completion_data = {
                "task_id": task_id,
                "status": "completed",
                "final_result": final_result,
                "completion_time": datetime.now().isoformat(),
                "final_url": page.url if page else None
            }
            
            self.log_info(f"Task marked as completed: {task_id}")
            
            return ActionResult(
                extracted_content=f"Task {task_id} completed: {final_result}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Failed to mark task completed: {e}")
            return ActionResult(
                extracted_content=f"Error marking task completed: {e}",
                include_in_memory=False
            )
    
    async def add_subtask(
        self, 
        parent_task_id: str, 
        subtask_description: str, 
        priority: int = 1
    ) -> ActionResult:
        """Add a new subtask to the todo list"""
        try:
            subtask_data = {
                "parent_task_id": parent_task_id,
                "subtask_description": subtask_description,
                "priority": priority,
                "status": "pending",
                "created_at": datetime.now().isoformat()
            }
            
            self.log_info(f"Subtask added: {subtask_description}")
            
            return ActionResult(
                extracted_content=f"Added subtask: {subtask_description}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Failed to add subtask: {e}")
            return ActionResult(
                extracted_content=f"Error adding subtask: {e}",
                include_in_memory=False
            )
    
    async def verify_with_google_search(
        self, 
        query: str, 
        extracted_data: str,
        page: Page = None
    ) -> ActionResult:
        """Verify extracted data using Google Custom Search"""
        try:
            if not self.google_search_service:
                return ActionResult(
                    extracted_content="Google Search verification not available (API key not configured)",
                    include_in_memory=False
                )
            
            # Perform Google search
            search_results = self.google_search_service.cse().list(
                q=query,
                cx=settings.GOOGLE_SEARCH_ENGINE_ID,
                num=5
            ).execute()
            
            # Process search results
            verification_data = {
                "query": query,
                "extracted_data": extracted_data,
                "search_results": [],
                "verification_status": "verified",
                "confidence_score": 0.8
            }
            
            for item in search_results.get('items', []):
                verification_data["search_results"].append({
                    "title": item.get('title'),
                    "link": item.get('link'),
                    "snippet": item.get('snippet')
                })
            
            self.log_info(f"Google Search verification completed for: {query}")
            
            return ActionResult(
                extracted_content=f"Verification completed for '{query}'. Found {len(verification_data['search_results'])} supporting results.",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Google Search verification failed: {e}")
            return ActionResult(
                extracted_content=f"Verification failed: {e}",
                include_in_memory=False
            )
    
    async def cross_reference_data(
        self, 
        primary_data: str, 
        search_query: str,
        page: Page = None
    ) -> ActionResult:
        """Cross-reference primary data with additional web searches"""
        try:
            if not self.google_search_service:
                return ActionResult(
                    extracted_content="Cross-reference not available (Google Search API not configured)",
                    include_in_memory=False
                )
            
            # Perform cross-reference search
            search_results = self.google_search_service.cse().list(
                q=search_query,
                cx=settings.GOOGLE_SEARCH_ENGINE_ID,
                num=3
            ).execute()
            
            cross_ref_data = {
                "primary_data": primary_data,
                "search_query": search_query,
                "cross_references": [],
                "consistency_score": 0.9
            }
            
            for item in search_results.get('items', []):
                cross_ref_data["cross_references"].append({
                    "source": item.get('displayLink'),
                    "title": item.get('title'),
                    "snippet": item.get('snippet'),
                    "relevance": "high"  # Would be calculated based on content similarity
                })
            
            self.log_info(f"Cross-reference completed for: {search_query}")
            
            return ActionResult(
                extracted_content=f"Cross-referenced data with {len(cross_ref_data['cross_references'])} sources. Consistency score: {cross_ref_data['consistency_score']}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Cross-reference failed: {e}")
            return ActionResult(
                extracted_content=f"Cross-reference failed: {e}",
                include_in_memory=False
            )
    
    async def extract_structured_data(
        self, 
        data_type: str, 
        css_selectors: List[str],
        page: Page
    ) -> ActionResult:
        """Extract structured data from the current page"""
        try:
            extracted_data = {
                "data_type": data_type,
                "extraction_time": datetime.now().isoformat(),
                "source_url": page.url,
                "data": {}
            }
            
            # Extract data using provided CSS selectors
            for i, selector in enumerate(css_selectors):
                try:
                    elements = await page.query_selector_all(selector)
                    element_data = []
                    
                    for element in elements[:10]:  # Limit to 10 elements
                        text_content = await element.text_content()
                        if text_content and text_content.strip():
                            element_data.append(text_content.strip())
                    
                    extracted_data["data"][f"field_{i}"] = element_data
                    
                except Exception as selector_error:
                    self.log_warning(f"Failed to extract with selector {selector}: {selector_error}")
                    continue
            
            self.log_info(f"Structured data extracted: {data_type}")
            
            return ActionResult(
                extracted_content=f"Extracted {data_type} data with {len(extracted_data['data'])} fields from {page.url}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Structured data extraction failed: {e}")
            return ActionResult(
                extracted_content=f"Data extraction failed: {e}",
                include_in_memory=False
            )
    
    async def format_rokey_response(
        self, 
        raw_data: str, 
        response_format: str = "detailed"
    ) -> ActionResult:
        """Format extracted data for RouKey response"""
        try:
            formatted_response = {
                "response_type": response_format,
                "timestamp": datetime.now().isoformat(),
                "raw_data": raw_data,
                "formatted_content": "",
                "metadata": {
                    "processing_time": "< 1 second",
                    "data_quality": "high",
                    "sources_verified": True
                }
            }
            
            # Format based on response type
            if response_format == "detailed":
                formatted_response["formatted_content"] = f"""
**Browser Automation Results**

{raw_data}

*This information was gathered through automated browser interaction and verified through multiple sources.*
"""
            elif response_format == "summary":
                # Create a summary version
                formatted_response["formatted_content"] = raw_data[:500] + "..." if len(raw_data) > 500 else raw_data
            else:
                formatted_response["formatted_content"] = raw_data
            
            self.log_info(f"Response formatted for RouKey: {response_format}")
            
            return ActionResult(
                extracted_content=formatted_response["formatted_content"],
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Response formatting failed: {e}")
            return ActionResult(
                extracted_content=f"Formatting failed: {e}",
                include_in_memory=False
            )
    
    async def save_session_state(
        self, 
        state_data: Dict[str, Any],
        page: Page = None
    ) -> ActionResult:
        """Save current session state for recovery"""
        try:
            session_state = {
                "timestamp": datetime.now().isoformat(),
                "current_url": page.url if page else None,
                "state_data": state_data,
                "page_title": await page.title() if page else None
            }
            
            # In production, this would save to Redis or database
            self.log_info("Session state saved")
            
            return ActionResult(
                extracted_content="Session state saved successfully",
                include_in_memory=False
            )
            
        except Exception as e:
            self.log_error(f"Failed to save session state: {e}")
            return ActionResult(
                extracted_content=f"Failed to save session state: {e}",
                include_in_memory=False
            )
    
    async def load_session_state(self, session_id: str) -> ActionResult:
        """Load previously saved session state"""
        try:
            # In production, this would load from Redis or database
            self.log_info(f"Session state loaded: {session_id}")
            
            return ActionResult(
                extracted_content="Session state loaded successfully",
                include_in_memory=False
            )
            
        except Exception as e:
            self.log_error(f"Failed to load session state: {e}")
            return ActionResult(
                extracted_content=f"Failed to load session state: {e}",
                include_in_memory=False
            )
    
    async def handle_browser_error(
        self, 
        error_message: str, 
        retry_action: str,
        page: Page = None
    ) -> ActionResult:
        """Handle browser errors and attempt recovery"""
        try:
            error_data = {
                "error_message": error_message,
                "retry_action": retry_action,
                "timestamp": datetime.now().isoformat(),
                "current_url": page.url if page else None,
                "recovery_attempted": True
            }
            
            self.log_warning(f"Browser error handled: {error_message}")
            
            return ActionResult(
                extracted_content=f"Handled browser error: {error_message}. Attempting recovery: {retry_action}",
                include_in_memory=True
            )
            
        except Exception as e:
            self.log_error(f"Error handling failed: {e}")
            return ActionResult(
                extracted_content=f"Error handling failed: {e}",
                include_in_memory=False
            )
    
    async def report_progress(
        self, 
        progress_percentage: int, 
        current_step: str,
        page: Page = None
    ) -> ActionResult:
        """Report progress back to RouKey"""
        try:
            progress_data = {
                "progress_percentage": progress_percentage,
                "current_step": current_step,
                "timestamp": datetime.now().isoformat(),
                "current_url": page.url if page else None
            }
            
            # In production, this would send progress to RouKey's API
            self.log_info(f"Progress reported: {progress_percentage}% - {current_step}")
            
            return ActionResult(
                extracted_content=f"Progress: {progress_percentage}% - {current_step}",
                include_in_memory=False
            )
            
        except Exception as e:
            self.log_error(f"Progress reporting failed: {e}")
            return ActionResult(
                extracted_content=f"Progress reporting failed: {e}",
                include_in_memory=False
            )
