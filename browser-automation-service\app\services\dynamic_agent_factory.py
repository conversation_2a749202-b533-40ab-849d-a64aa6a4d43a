"""
Dynamic Agent Factory
Creates browser automation agents dynamically based on user configurations
"""

from typing import Dict, List, Any, Optional
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON>hropic
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>
from langchain_core.messages import SystemMessage
from browser_use import Agent as BrowserUseAgent, Controller
from browser_use.agent.memory import MemoryConfig

from app.services.session_manager import SessionManager
from app.services.browser_functions import BrowserFunctions
from app.services.browser_execution_agent import BrowserExecutionAgent
from app.services.memory_integration import memory_integration
from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class DynamicBrowserAgent(LoggerMixin):
    """Dynamic browser agent that uses user's configured LLM and Browser Use"""
    
    def __init__(
        self,
        role: str,
        llm_config: Dict[str, Any],
        browser_functions: BrowserFunctions,
        memory_config: Optional[MemoryConfig] = None,
        system_prompt: Optional[str] = None
    ):
        self.role = role
        self.llm_config = llm_config
        self.browser_functions = browser_functions
        self.memory_config = memory_config
        self.system_prompt = system_prompt
        
        # Create LLM instance
        self.llm = self._create_llm()
        
        # Create Browser Use agent
        self.browser_agent = self._create_browser_agent()
        
        self.log_info(f"Dynamic browser agent created for role: {role}")
    
    def _create_llm(self):
        """Create LLM instance based on user configuration"""
        provider = self.llm_config.get("provider", "openai").lower()
        model = self.llm_config.get("model", "gpt-4o")
        api_key = self.llm_config.get("api_key")
        
        if not api_key:
            raise BrowserAutomationException(f"No API key provided for {provider}")
        
        common_params = {
            "temperature": self.llm_config.get("temperature", 0.7),
            "max_tokens": self.llm_config.get("max_tokens", 2000)
        }
        
        if provider == "openai":
            return ChatOpenAI(
                api_key=api_key,
                model=model,
                **common_params
            )
        elif provider == "anthropic":
            return ChatAnthropic(
                api_key=api_key,
                model=model,
                **common_params
            )
        elif provider == "google":
            return ChatGoogleGenerativeAI(
                api_key=api_key,
                model=model,
                **common_params
            )
        else:
            raise BrowserAutomationException(f"Unsupported LLM provider: {provider}")
    
    def _create_browser_agent(self) -> BrowserUseAgent:
        """Create Browser Use agent with custom functions"""
        
        # Create controller with custom functions
        controller = Controller()
        
        # Add custom browser functions
        self.browser_functions.register_functions(controller)
        
        # Create Browser Use agent
        agent = BrowserUseAgent(
            task="",  # Will be set during execution
            llm=self.llm,
            controller=controller,
            use_vision=True,
            enable_memory=self.memory_config is not None,
            memory_config=self.memory_config,
            override_system_message=self.system_prompt
        )
        
        return agent
    
    async def execute(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent with current state"""
        try:
            # Extract task from state
            messages = state.get("messages", [])
            current_task = messages[-1].content if messages else "Complete the browser automation task"
            
            # Update agent task
            self.browser_agent.task = current_task
            
            self.log_info(f"Executing {self.role} agent", task=current_task[:100])
            
            # Execute Browser Use agent
            result = await self.browser_agent.run(max_steps=10)  # Limit steps per agent
            
            # Update state with results
            updated_state = state.copy()
            updated_state.update({
                "last_agent": self.role,
                "agent_result": str(result),
                "tokens_used": updated_state.get("tokens_used", 0) + 100,  # Estimate
                "llm_calls": updated_state.get("llm_calls", 0) + 1
            })
            
            self.log_info(f"{self.role} agent execution completed")
            
            return updated_state
            
        except Exception as e:
            self.log_error(f"Agent execution failed: {e}", role=self.role)
            raise BrowserAutomationException(f"Agent {self.role} execution failed: {e}")


class DynamicSupervisorAgent(LoggerMixin):
    """Dynamic supervisor agent for coordinating other agents"""
    
    def __init__(
        self,
        available_roles: List[str],
        llm_config: Dict[str, Any],
        system_prompt: Optional[str] = None
    ):
        self.available_roles = available_roles
        self.llm_config = llm_config
        self.system_prompt = system_prompt or self._get_default_supervisor_prompt()
        
        # Create LLM instance
        self.llm = self._create_llm()
        
        self.log_info(f"Dynamic supervisor agent created with roles: {available_roles}")
    
    def _create_llm(self):
        """Create LLM instance for supervisor"""
        provider = self.llm_config.get("provider", "openai").lower()
        model = self.llm_config.get("model", "gpt-4o")
        api_key = self.llm_config.get("api_key")
        
        if provider == "openai":
            return ChatOpenAI(
                api_key=api_key,
                model=model,
                temperature=0.3,  # Lower temperature for coordination
                max_tokens=1000
            )
        elif provider == "anthropic":
            return ChatAnthropic(
                api_key=api_key,
                model=model,
                temperature=0.3,
                max_tokens=1000
            )
        else:
            # Fallback to OpenAI
            return ChatOpenAI(
                api_key=settings.OPENAI_API_KEY,
                model="gpt-4o",
                temperature=0.3,
                max_tokens=1000
            )
    
    def _get_default_supervisor_prompt(self) -> str:
        """Get default system prompt for supervisor"""
        return f"""You are a supervisor agent coordinating browser automation tasks.

Available roles: {', '.join(self.available_roles)}

Your responsibilities:
1. Analyze the main task and break it down into subtasks
2. Assign subtasks to appropriate roles
3. Monitor progress and coordinate between agents
4. Decide when the task is complete
5. Provide final results to the user

Always respond with clear decisions about which role should handle the next subtask.
If the task is complete, respond with "TASK_COMPLETE" and a summary.

Be efficient and direct in your coordination."""
    
    async def execute(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Execute supervisor coordination logic"""
        try:
            messages = state.get("messages", [])
            current_state = state.get("state", {})
            
            # Create coordination prompt
            coordination_prompt = self._create_coordination_prompt(messages, current_state)
            
            # Get supervisor decision
            response = await self.llm.ainvoke([
                SystemMessage(content=self.system_prompt),
                coordination_prompt
            ])
            
            # Process supervisor decision
            decision = response.content
            updated_state = self._process_supervisor_decision(state, decision)
            
            self.log_info("Supervisor coordination completed", decision=decision[:100])
            
            return updated_state
            
        except Exception as e:
            self.log_error(f"Supervisor execution failed: {e}")
            raise BrowserAutomationException(f"Supervisor coordination failed: {e}")
    
    def _create_coordination_prompt(self, messages: List, current_state: Dict[str, Any]):
        """Create prompt for supervisor coordination"""
        from langchain_core.messages import HumanMessage
        
        # Analyze current state and create coordination prompt
        todo_list = current_state.get("todo_list", [])
        last_agent = current_state.get("last_agent")
        agent_result = current_state.get("agent_result")
        
        prompt = f"""
Current task: {messages[0].content if messages else 'No task specified'}

Current state:
- Todo list: {len(todo_list)} items
- Last agent: {last_agent}
- Last result: {agent_result[:200] if agent_result else 'None'}

Available roles: {', '.join(self.available_roles)}

What should happen next? Which role should handle the next subtask?
"""
        
        return HumanMessage(content=prompt)
    
    def _process_supervisor_decision(self, state: Dict[str, Any], decision: str) -> Dict[str, Any]:
        """Process supervisor decision and update state"""
        updated_state = state.copy()
        
        # Simple decision processing - can be made more sophisticated
        if "TASK_COMPLETE" in decision:
            updated_state["task_complete"] = True
            updated_state["final_result"] = decision
        else:
            updated_state["supervisor_decision"] = decision
            updated_state["next_agent"] = self._extract_next_agent(decision)
        
        return updated_state
    
    def _extract_next_agent(self, decision: str) -> str:
        """Extract next agent from supervisor decision"""
        decision_lower = decision.lower()
        
        for role in self.available_roles:
            if role.lower() in decision_lower:
                return role
        
        # Default to first available role
        return self.available_roles[0] if self.available_roles else "general_chat"


class DynamicAgentFactory(LoggerMixin):
    """Factory for creating dynamic browser automation agents"""
    
    def __init__(self):
        self.browser_functions = BrowserFunctions()
    
    async def create_browser_agent(
        self,
        role: str,
        user_config: Dict[str, Any],
        routing_strategy: str,
        enable_memory: bool = True,
        session_manager: Optional[SessionManager] = None
    ) -> DynamicBrowserAgent:
        """Create a dynamic browser agent for a specific role"""
        
        # Get LLM configuration for role
        llm_config = await self._get_llm_config_for_role(role, user_config, routing_strategy)
        
        # Create memory configuration if enabled
        memory_config = None
        if enable_memory:
            memory_config = self._create_memory_config(llm_config, role)
        
        # Get role-specific system prompt
        system_prompt = self._get_system_prompt_for_role(role)
        
        # Create agent
        agent = DynamicBrowserAgent(
            role=role,
            llm_config=llm_config,
            browser_functions=self.browser_functions,
            memory_config=memory_config,
            system_prompt=system_prompt
        )
        
        self.log_info(f"Created browser agent for role: {role}")
        return agent
    
    async def create_supervisor_agent(
        self,
        available_roles: List[str],
        user_config: Dict[str, Any],
        routing_strategy: str
    ) -> DynamicSupervisorAgent:
        """Create a dynamic supervisor agent"""
        
        # Use general_chat role for supervisor or first available role
        supervisor_role = "general_chat" if "general_chat" in available_roles else available_roles[0]
        llm_config = await self._get_llm_config_for_role(supervisor_role, user_config, routing_strategy)
        
        agent = DynamicSupervisorAgent(
            available_roles=available_roles,
            llm_config=llm_config
        )
        
        self.log_info(f"Created supervisor agent with roles: {available_roles}")
        return agent
    
    async def _get_llm_config_for_role(
        self,
        role: str,
        user_config: Dict[str, Any],
        routing_strategy: str
    ) -> Dict[str, Any]:
        """Get LLM configuration for a specific role"""
        
        roles_config = user_config.get("roles", {})
        role_config = roles_config.get(role)
        
        if not role_config:
            # Fallback to general_chat or first available role
            fallback_role = "general_chat"
            if fallback_role not in roles_config and roles_config:
                fallback_role = list(roles_config.keys())[0]
            role_config = roles_config.get(fallback_role, {})
        
        # Get first API key for the role (routing strategy would be applied here)
        api_keys = role_config.get("api_keys", [])
        if not api_keys:
            raise BrowserAutomationException(f"No API keys configured for role: {role}")
        
        selected_key = api_keys[0]  # Simple selection for now
        
        return {
            "api_key": selected_key.get("key"),
            "provider": selected_key.get("provider"),
            "model": selected_key.get("model"),
            "temperature": role_config.get("temperature", 0.7),
            "max_tokens": role_config.get("max_tokens", 2000)
        }
    
    def _create_memory_config(self, llm_config: Dict[str, Any], role: str) -> MemoryConfig:
        """Create memory configuration for Browser Use"""
        
        # Create LLM instance for memory
        provider = llm_config.get("provider", "openai").lower()
        api_key = llm_config.get("api_key")
        
        if provider == "openai":
            memory_llm = ChatOpenAI(api_key=api_key, model="gpt-4o")
        elif provider == "anthropic":
            memory_llm = ChatAnthropic(api_key=api_key, model="claude-3-sonnet-20240229")
        else:
            # Fallback to OpenAI
            memory_llm = ChatOpenAI(api_key=settings.OPENAI_API_KEY, model="gpt-4o")
        
        return MemoryConfig(
            llm_instance=memory_llm,
            agent_id=f"rokey_browser_{role}",
            memory_interval=10,
            vector_store_provider="qdrant",
            vector_store_config_override={
                "host": settings.QDRANT_HOST,
                "port": settings.QDRANT_PORT
            }
        )
    
    def _get_system_prompt_for_role(self, role: str) -> str:
        """Get system prompt for a specific role"""
        
        role_prompts = {
            "general_chat": """You are a helpful AI assistant specializing in browser automation. 
You can navigate websites, extract information, fill forms, and perform various web-based tasks.
Be efficient and accurate in your browser interactions.""",
            
            "web_research": """You are a web research specialist. Your expertise is in:
- Finding accurate information online
- Navigating complex websites
- Extracting and organizing data
- Verifying information from multiple sources
Focus on thorough research and accurate data extraction.""",
            
            "shopping_assistant": """You are a shopping assistant specializing in:
- Finding products and comparing prices
- Navigating e-commerce websites
- Extracting product details and reviews
- Identifying the best deals and offers
Be thorough in price comparison and product analysis."""
        }
        
        return role_prompts.get(role, role_prompts["general_chat"])
