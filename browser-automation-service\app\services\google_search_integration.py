"""
Google Search Integration Service
Advanced Google Custom Search API integration with specialized search capabilities
"""

import asyncio
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import httpx
from urllib.parse import urlparse, quote_plus

from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class GoogleSearchIntegration(LoggerMixin):
    """
    Advanced Google Search integration for verification and research
    
    Features:
    - Custom Search API with advanced parameters
    - Specialized search types (news, shopping, academic)
    - Rate limiting and quota management
    - Search result enrichment and analysis
    - Domain-specific search optimization
    - Real-time search trends integration
    """
    
    def __init__(self):
        self.service = None
        self.search_quota_used = 0
        self.daily_quota_limit = 100  # Google Custom Search free tier limit
        self.rate_limiter = asyncio.Semaphore(10)  # Max 10 concurrent searches
        
        # Search result cache
        self.search_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 3600  # 1 hour cache TTL
        
        # Specialized search configurations
        self.search_configs = {
            "news": {
                "sort": "date",
                "dateRestrict": "d1",  # Last day
                "siteSearch": "news.google.com OR cnn.com OR bbc.com OR reuters.com"
            },
            "academic": {
                "siteSearch": "scholar.google.com OR pubmed.ncbi.nlm.nih.gov OR arxiv.org"
            },
            "shopping": {
                "siteSearch": "amazon.com OR ebay.com OR walmart.com OR bestbuy.com OR target.com"
            },
            "government": {
                "siteSearch": "*.gov"
            },
            "social": {
                "siteSearch": "twitter.com OR reddit.com OR facebook.com"
            }
        }
    
    async def initialize(self):
        """Initialize Google Search integration"""
        try:
            if not settings.GOOGLE_SEARCH_API_KEY or not settings.GOOGLE_SEARCH_ENGINE_ID:
                raise BrowserAutomationException("Google Search API credentials not configured")
            
            self.service = build(
                "customsearch",
                "v1",
                developerKey=settings.GOOGLE_SEARCH_API_KEY
            )
            
            # Test API connection
            await self._test_api_connection()
            
            self.log_info("Google Search integration initialized successfully")
            
        except Exception as e:
            self.log_error(f"Failed to initialize Google Search integration: {e}")
            raise BrowserAutomationException(f"Google Search initialization failed: {e}")
    
    async def search(
        self,
        query: str,
        search_type: str = "general",
        num_results: int = 10,
        language: str = "en",
        country: str = "us",
        safe_search: str = "active",
        date_restrict: str = None,
        site_search: str = None,
        exclude_sites: List[str] = None
    ) -> Dict[str, Any]:
        """
        Perform advanced Google search with customizable parameters
        
        Args:
            query: Search query
            search_type: Type of search (general, news, academic, shopping, etc.)
            num_results: Number of results to return (max 10 per request)
            language: Search language
            country: Country for search results
            safe_search: Safe search setting
            date_restrict: Date restriction (d1, w1, m1, y1)
            site_search: Specific site to search
            exclude_sites: Sites to exclude from results
            
        Returns:
            Dictionary containing search results and metadata
        """
        try:
            async with self.rate_limiter:
                # Check quota
                if self.search_quota_used >= self.daily_quota_limit:
                    raise BrowserAutomationException("Daily search quota exceeded")
                
                # Check cache
                cache_key = self._generate_cache_key(query, search_type, num_results)
                cached_result = self._get_cached_result(cache_key)
                if cached_result:
                    self.log_info(f"Returning cached search result for: {query[:50]}")
                    return cached_result
                
                # Prepare search parameters
                search_params = await self._prepare_search_params(
                    query, search_type, num_results, language, country,
                    safe_search, date_restrict, site_search, exclude_sites
                )
                
                # Perform search
                self.log_info(f"Performing Google search: {query[:50]}")
                
                search_result = self.service.cse().list(**search_params).execute()
                
                # Process and enrich results
                processed_result = await self._process_search_results(
                    search_result, query, search_type
                )
                
                # Cache result
                self._cache_result(cache_key, processed_result)
                
                # Update quota
                self.search_quota_used += 1
                
                self.log_info(
                    f"Search completed: {len(processed_result.get('items', []))} results",
                    quota_used=self.search_quota_used
                )
                
                return processed_result
                
        except HttpError as e:
            self.log_error(f"Google Search API error: {e}")
            raise BrowserAutomationException(f"Search API error: {e}")
        except Exception as e:
            self.log_error(f"Search failed: {e}")
            raise BrowserAutomationException(f"Search failed: {e}")
    
    async def search_news(
        self,
        query: str,
        time_period: str = "d1",
        num_results: int = 5
    ) -> Dict[str, Any]:
        """Search for recent news articles"""
        return await self.search(
            query=query,
            search_type="news",
            num_results=num_results,
            date_restrict=time_period
        )
    
    async def search_academic(
        self,
        query: str,
        num_results: int = 5
    ) -> Dict[str, Any]:
        """Search academic and research sources"""
        return await self.search(
            query=query,
            search_type="academic",
            num_results=num_results
        )
    
    async def search_shopping(
        self,
        product_query: str,
        price_range: str = None,
        num_results: int = 10
    ) -> Dict[str, Any]:
        """Search for product and pricing information"""
        
        # Enhance query with price information if provided
        enhanced_query = product_query
        if price_range:
            enhanced_query += f" price {price_range}"
        
        return await self.search(
            query=enhanced_query,
            search_type="shopping",
            num_results=num_results
        )
    
    async def search_multiple_queries(
        self,
        queries: List[str],
        search_type: str = "general",
        max_concurrent: int = 3
    ) -> List[Dict[str, Any]]:
        """Search multiple queries with concurrency control"""
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def search_single(query: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    return await self.search(query, search_type=search_type)
                except Exception as e:
                    self.log_warning(f"Search failed for query '{query}': {e}")
                    return {"query": query, "error": str(e), "items": []}
        
        # Execute searches concurrently
        tasks = [search_single(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                self.log_warning(f"Search task failed: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def extract_search_insights(
        self,
        search_results: Dict[str, Any],
        focus_area: str = "general"
    ) -> Dict[str, Any]:
        """Extract insights and patterns from search results"""
        
        items = search_results.get("items", [])
        if not items:
            return {"insights": [], "patterns": [], "summary": "No results to analyze"}
        
        insights = {
            "total_results": len(items),
            "domains_found": [],
            "common_themes": [],
            "credibility_analysis": {},
            "temporal_analysis": {},
            "content_analysis": {},
            "focus_area": focus_area
        }
        
        # Analyze domains
        domain_counts = {}
        for item in items:
            domain = urlparse(item.get("link", "")).netloc
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
            if domain not in insights["domains_found"]:
                insights["domains_found"].append(domain)
        
        # Find most common domains
        insights["top_domains"] = sorted(
            domain_counts.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:5]
        
        # Analyze content themes
        all_text = " ".join([
            item.get("title", "") + " " + item.get("snippet", "")
            for item in items
        ])
        
        insights["common_themes"] = await self._extract_themes(all_text)
        
        # Credibility analysis
        insights["credibility_analysis"] = await self._analyze_source_credibility(items)
        
        # Temporal analysis (if dates available)
        insights["temporal_analysis"] = await self._analyze_temporal_patterns(items)
        
        return insights
    
    async def verify_information_consistency(
        self,
        primary_claim: str,
        search_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Verify consistency of information across search results"""
        
        consistency_analysis = {
            "primary_claim": primary_claim,
            "total_sources": len(search_results),
            "supporting_sources": 0,
            "conflicting_sources": 0,
            "neutral_sources": 0,
            "consistency_score": 0.0,
            "confidence_level": "low",
            "analysis_details": []
        }
        
        # Analyze each search result against the primary claim
        for result in search_results:
            source_analysis = await self._analyze_source_consistency(
                primary_claim, result
            )
            consistency_analysis["analysis_details"].append(source_analysis)
            
            # Count source types
            if source_analysis["stance"] == "supporting":
                consistency_analysis["supporting_sources"] += 1
            elif source_analysis["stance"] == "conflicting":
                consistency_analysis["conflicting_sources"] += 1
            else:
                consistency_analysis["neutral_sources"] += 1
        
        # Calculate consistency score
        total_sources = consistency_analysis["total_sources"]
        if total_sources > 0:
            supporting_ratio = consistency_analysis["supporting_sources"] / total_sources
            conflicting_ratio = consistency_analysis["conflicting_sources"] / total_sources
            
            # Score based on support vs conflict ratio
            consistency_analysis["consistency_score"] = max(0, supporting_ratio - conflicting_ratio)
            
            # Determine confidence level
            if consistency_analysis["consistency_score"] > 0.7:
                consistency_analysis["confidence_level"] = "high"
            elif consistency_analysis["consistency_score"] > 0.4:
                consistency_analysis["confidence_level"] = "medium"
            else:
                consistency_analysis["confidence_level"] = "low"
        
        return consistency_analysis
    
    async def _prepare_search_params(
        self,
        query: str,
        search_type: str,
        num_results: int,
        language: str,
        country: str,
        safe_search: str,
        date_restrict: str,
        site_search: str,
        exclude_sites: List[str]
    ) -> Dict[str, Any]:
        """Prepare search parameters for Google Custom Search API"""
        
        params = {
            "q": query,
            "cx": settings.GOOGLE_SEARCH_ENGINE_ID,
            "num": min(num_results, 10),  # API limit is 10 per request
            "lr": f"lang_{language}",
            "gl": country,
            "safe": safe_search
        }
        
        # Apply search type configuration
        if search_type in self.search_configs:
            config = self.search_configs[search_type]
            params.update(config)
        
        # Apply additional parameters
        if date_restrict:
            params["dateRestrict"] = date_restrict
        
        if site_search:
            params["siteSearch"] = site_search
        
        if exclude_sites:
            # Add site exclusions to query
            exclusions = " ".join([f"-site:{site}" for site in exclude_sites])
            params["q"] += f" {exclusions}"
        
        return params
    
    async def _process_search_results(
        self,
        raw_results: Dict[str, Any],
        query: str,
        search_type: str
    ) -> Dict[str, Any]:
        """Process and enrich search results"""
        
        processed = {
            "query": query,
            "search_type": search_type,
            "timestamp": datetime.now().isoformat(),
            "total_results": raw_results.get("searchInformation", {}).get("totalResults", "0"),
            "search_time": raw_results.get("searchInformation", {}).get("searchTime", 0),
            "items": [],
            "metadata": {
                "quota_used": self.search_quota_used + 1,
                "cache_status": "fresh"
            }
        }
        
        # Process each search result item
        for item in raw_results.get("items", []):
            processed_item = {
                "title": item.get("title", ""),
                "link": item.get("link", ""),
                "snippet": item.get("snippet", ""),
                "display_link": item.get("displayLink", ""),
                "formatted_url": item.get("formattedUrl", ""),
                "domain": urlparse(item.get("link", "")).netloc,
                "relevance_score": await self._calculate_relevance_score(item, query),
                "content_type": await self._detect_content_type(item),
                "language": await self._detect_language(item),
                "publication_date": await self._extract_publication_date(item)
            }
            
            # Add search type specific enrichments
            if search_type == "shopping":
                processed_item.update(await self._enrich_shopping_result(item))
            elif search_type == "news":
                processed_item.update(await self._enrich_news_result(item))
            elif search_type == "academic":
                processed_item.update(await self._enrich_academic_result(item))
            
            processed["items"].append(processed_item)
        
        return processed
    
    async def _test_api_connection(self):
        """Test Google Search API connection"""
        try:
            test_result = self.service.cse().list(
                q="test",
                cx=settings.GOOGLE_SEARCH_ENGINE_ID,
                num=1
            ).execute()
            
            if "items" in test_result:
                self.log_info("Google Search API connection test successful")
            else:
                raise BrowserAutomationException("API test returned no results")
                
        except Exception as e:
            raise BrowserAutomationException(f"API connection test failed: {e}")
    
    def _generate_cache_key(self, query: str, search_type: str, num_results: int) -> str:
        """Generate cache key for search results"""
        import hashlib
        key_string = f"{query}_{search_type}_{num_results}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached search result if still valid"""
        if cache_key in self.search_cache:
            cached_data = self.search_cache[cache_key]
            cache_time = datetime.fromisoformat(cached_data["cached_at"])
            
            if (datetime.now() - cache_time).seconds < self.cache_ttl:
                cached_data["metadata"]["cache_status"] = "hit"
                return cached_data
            else:
                # Remove expired cache entry
                del self.search_cache[cache_key]
        
        return None
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache search result"""
        result["cached_at"] = datetime.now().isoformat()
        self.search_cache[cache_key] = result
        
        # Limit cache size
        if len(self.search_cache) > 100:
            # Remove oldest entries
            oldest_key = min(
                self.search_cache.keys(),
                key=lambda k: self.search_cache[k]["cached_at"]
            )
            del self.search_cache[oldest_key]
    
    # Helper methods for result processing and analysis
    async def _calculate_relevance_score(self, item: Dict, query: str) -> float:
        """Calculate relevance score for search result"""
        # Simple relevance scoring based on query terms in title and snippet
        title = item.get("title", "").lower()
        snippet = item.get("snippet", "").lower()
        query_terms = query.lower().split()
        
        score = 0.0
        for term in query_terms:
            if term in title:
                score += 0.3
            if term in snippet:
                score += 0.2
        
        return min(score, 1.0)
    
    async def _detect_content_type(self, item: Dict) -> str:
        """Detect content type of search result"""
        link = item.get("link", "").lower()
        title = item.get("title", "").lower()
        
        if any(ext in link for ext in [".pdf", ".doc", ".docx"]):
            return "document"
        elif any(word in title for word in ["video", "watch", "youtube"]):
            return "video"
        elif any(word in title for word in ["image", "photo", "picture"]):
            return "image"
        else:
            return "webpage"
    
    async def _detect_language(self, item: Dict) -> str:
        """Detect language of search result"""
        # Simple language detection (in production, use proper language detection)
        return "en"  # Default to English
    
    async def _extract_publication_date(self, item: Dict) -> Optional[str]:
        """Extract publication date from search result"""
        # This would parse dates from snippets or metadata
        return None  # Placeholder
    
    async def _enrich_shopping_result(self, item: Dict) -> Dict[str, Any]:
        """Enrich shopping search results with price and product info"""
        return {"product_type": "unknown", "price_found": False}
    
    async def _enrich_news_result(self, item: Dict) -> Dict[str, Any]:
        """Enrich news search results with publication info"""
        return {"news_source": item.get("displayLink", ""), "article_type": "news"}
    
    async def _enrich_academic_result(self, item: Dict) -> Dict[str, Any]:
        """Enrich academic search results with research info"""
        return {"academic_source": True, "peer_reviewed": "unknown"}
    
    async def _extract_themes(self, text: str) -> List[str]:
        """Extract common themes from text"""
        # Simple theme extraction (in production, use NLP)
        return ["technology", "business", "research"]  # Placeholder
    
    async def _analyze_source_credibility(self, items: List[Dict]) -> Dict[str, Any]:
        """Analyze credibility of sources"""
        return {"average_credibility": 0.7, "high_credibility_sources": 3}
    
    async def _analyze_temporal_patterns(self, items: List[Dict]) -> Dict[str, Any]:
        """Analyze temporal patterns in search results"""
        return {"recent_results": 5, "date_range": "varied"}
    
    async def _analyze_source_consistency(self, claim: str, result: Dict) -> Dict[str, Any]:
        """Analyze how consistent a source is with a claim"""
        return {"stance": "supporting", "confidence": 0.8, "reasoning": "Content aligns with claim"}
    
    async def cleanup(self):
        """Cleanup Google Search integration resources"""
        self.search_cache.clear()
        self.log_info("Google Search integration cleanup completed")
