"""
Parallel Processing Manager
Coordinates parallel verification tasks and browser automation workflows
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Awaitable, Union
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


@dataclass
class TaskResult:
    """Result of a parallel task execution"""
    task_id: str
    success: bool
    result: Any
    error: Optional[str]
    execution_time: float
    timestamp: str


@dataclass
class ParallelWorkflow:
    """Configuration for parallel workflow execution"""
    workflow_id: str
    tasks: List[Dict[str, Any]]
    max_concurrent: int
    timeout: float
    retry_attempts: int
    failure_threshold: float  # Percentage of tasks that can fail


class ParallelProcessor(LoggerMixin):
    """
    Advanced parallel processing manager for browser automation
    
    Features:
    - Concurrent task execution with configurable limits
    - Task dependency management
    - Error handling and retry mechanisms
    - Resource pooling and optimization
    - Progress tracking and monitoring
    - Workflow orchestration
    - Performance analytics
    """
    
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.thread_executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # Active workflows and tasks
        self.active_workflows: Dict[str, ParallelWorkflow] = {}
        self.task_results: Dict[str, List[TaskResult]] = {}
        
        # Performance tracking
        self.execution_stats = {
            "total_workflows": 0,
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "average_execution_time": 0.0,
            "concurrent_peak": 0
        }
        
        # Resource management
        self.resource_semaphores: Dict[str, asyncio.Semaphore] = {}
        self.active_tasks: Dict[str, asyncio.Task] = {}
        
        self.log_info(f"Parallel processor initialized with {max_workers} workers")
    
    async def execute_parallel_workflow(
        self,
        workflow: ParallelWorkflow,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Execute a parallel workflow with multiple tasks
        
        Args:
            workflow: Parallel workflow configuration
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dictionary containing workflow results and statistics
        """
        try:
            self.log_info(f"Starting parallel workflow: {workflow.workflow_id}")
            
            # Register workflow
            self.active_workflows[workflow.workflow_id] = workflow
            self.task_results[workflow.workflow_id] = []
            
            start_time = time.time()
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(workflow.max_concurrent)
            
            # Prepare tasks for execution
            task_coroutines = []
            for i, task_config in enumerate(workflow.tasks):
                task_id = f"{workflow.workflow_id}_task_{i}"
                
                # Create task coroutine with semaphore
                task_coro = self._execute_single_task(
                    task_id=task_id,
                    task_config=task_config,
                    semaphore=semaphore,
                    timeout=workflow.timeout,
                    retry_attempts=workflow.retry_attempts
                )
                
                task_coroutines.append(task_coro)
            
            # Execute tasks with progress tracking
            results = await self._execute_with_progress(
                task_coroutines, 
                workflow.workflow_id,
                progress_callback
            )
            
            # Analyze results
            execution_time = time.time() - start_time
            workflow_result = await self._analyze_workflow_results(
                workflow, results, execution_time
            )
            
            # Update statistics
            self._update_execution_stats(workflow, results, execution_time)
            
            self.log_info(
                f"Parallel workflow completed: {workflow.workflow_id}",
                execution_time=execution_time,
                success_rate=workflow_result["success_rate"]
            )
            
            return workflow_result
            
        except Exception as e:
            self.log_error(f"Parallel workflow failed: {e}")
            raise BrowserAutomationException(f"Workflow execution failed: {e}")
        finally:
            # Cleanup workflow
            if workflow.workflow_id in self.active_workflows:
                del self.active_workflows[workflow.workflow_id]
    
    async def execute_verification_tasks(
        self,
        verification_tasks: List[Dict[str, Any]],
        max_concurrent: int = 5,
        timeout: float = 30.0
    ) -> List[TaskResult]:
        """
        Execute verification tasks in parallel
        
        Specialized method for verification workflows
        """
        try:
            self.log_info(f"Executing {len(verification_tasks)} verification tasks")
            
            # Create workflow for verification
            workflow = ParallelWorkflow(
                workflow_id=f"verification_{int(time.time())}",
                tasks=verification_tasks,
                max_concurrent=max_concurrent,
                timeout=timeout,
                retry_attempts=2,
                failure_threshold=0.3  # Allow 30% failure rate
            )
            
            # Execute workflow
            workflow_result = await self.execute_parallel_workflow(workflow)
            
            return workflow_result["task_results"]
            
        except Exception as e:
            self.log_error(f"Verification tasks execution failed: {e}")
            raise BrowserAutomationException(f"Verification execution failed: {e}")
    
    async def execute_browser_actions(
        self,
        browser_actions: List[Dict[str, Any]],
        max_concurrent: int = 3,
        timeout: float = 60.0
    ) -> List[TaskResult]:
        """
        Execute browser actions in parallel
        
        Specialized method for browser automation workflows
        """
        try:
            self.log_info(f"Executing {len(browser_actions)} browser actions")
            
            # Create workflow for browser actions
            workflow = ParallelWorkflow(
                workflow_id=f"browser_actions_{int(time.time())}",
                tasks=browser_actions,
                max_concurrent=max_concurrent,
                timeout=timeout,
                retry_attempts=1,
                failure_threshold=0.2  # Allow 20% failure rate
            )
            
            # Execute workflow
            workflow_result = await self.execute_parallel_workflow(workflow)
            
            return workflow_result["task_results"]
            
        except Exception as e:
            self.log_error(f"Browser actions execution failed: {e}")
            raise BrowserAutomationException(f"Browser actions execution failed: {e}")
    
    async def execute_with_dependencies(
        self,
        tasks_with_deps: Dict[str, Dict[str, Any]],
        max_concurrent: int = 5
    ) -> Dict[str, TaskResult]:
        """
        Execute tasks with dependency management
        
        Args:
            tasks_with_deps: Dictionary where key is task_id and value contains
                           task config and dependencies list
        """
        try:
            self.log_info(f"Executing {len(tasks_with_deps)} tasks with dependencies")
            
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(tasks_with_deps)
            
            # Execute tasks in dependency order
            results = {}
            completed_tasks = set()
            semaphore = asyncio.Semaphore(max_concurrent)
            
            while len(completed_tasks) < len(tasks_with_deps):
                # Find tasks ready to execute (dependencies satisfied)
                ready_tasks = []
                for task_id, task_info in tasks_with_deps.items():
                    if task_id not in completed_tasks:
                        dependencies = task_info.get("dependencies", [])
                        if all(dep in completed_tasks for dep in dependencies):
                            ready_tasks.append(task_id)
                
                if not ready_tasks:
                    # Check for circular dependencies
                    remaining_tasks = set(tasks_with_deps.keys()) - completed_tasks
                    raise BrowserAutomationException(
                        f"Circular dependency detected in tasks: {remaining_tasks}"
                    )
                
                # Execute ready tasks in parallel
                task_coroutines = []
                for task_id in ready_tasks:
                    task_config = tasks_with_deps[task_id]
                    task_coro = self._execute_single_task(
                        task_id=task_id,
                        task_config=task_config,
                        semaphore=semaphore,
                        timeout=30.0,
                        retry_attempts=1
                    )
                    task_coroutines.append(task_coro)
                
                # Wait for batch completion
                batch_results = await asyncio.gather(*task_coroutines, return_exceptions=True)
                
                # Process batch results
                for i, result in enumerate(batch_results):
                    task_id = ready_tasks[i]
                    if isinstance(result, Exception):
                        results[task_id] = TaskResult(
                            task_id=task_id,
                            success=False,
                            result=None,
                            error=str(result),
                            execution_time=0.0,
                            timestamp=datetime.now().isoformat()
                        )
                    else:
                        results[task_id] = result
                    
                    completed_tasks.add(task_id)
            
            self.log_info(f"Dependency-based execution completed: {len(results)} tasks")
            
            return results
            
        except Exception as e:
            self.log_error(f"Dependency execution failed: {e}")
            raise BrowserAutomationException(f"Dependency execution failed: {e}")
    
    async def _execute_single_task(
        self,
        task_id: str,
        task_config: Dict[str, Any],
        semaphore: asyncio.Semaphore,
        timeout: float,
        retry_attempts: int
    ) -> TaskResult:
        """Execute a single task with retry logic"""
        
        async with semaphore:
            start_time = time.time()
            
            for attempt in range(retry_attempts + 1):
                try:
                    self.log_debug(f"Executing task {task_id}, attempt {attempt + 1}")
                    
                    # Get task function and parameters
                    task_function = task_config.get("function")
                    task_params = task_config.get("params", {})
                    
                    if not task_function:
                        raise BrowserAutomationException("Task function not specified")
                    
                    # Execute task with timeout
                    result = await asyncio.wait_for(
                        task_function(**task_params),
                        timeout=timeout
                    )
                    
                    execution_time = time.time() - start_time
                    
                    return TaskResult(
                        task_id=task_id,
                        success=True,
                        result=result,
                        error=None,
                        execution_time=execution_time,
                        timestamp=datetime.now().isoformat()
                    )
                    
                except asyncio.TimeoutError:
                    error_msg = f"Task {task_id} timed out after {timeout}s"
                    self.log_warning(error_msg)
                    
                    if attempt == retry_attempts:
                        execution_time = time.time() - start_time
                        return TaskResult(
                            task_id=task_id,
                            success=False,
                            result=None,
                            error=error_msg,
                            execution_time=execution_time,
                            timestamp=datetime.now().isoformat()
                        )
                    
                    # Wait before retry
                    await asyncio.sleep(1.0 * (attempt + 1))
                    
                except Exception as e:
                    error_msg = f"Task {task_id} failed: {str(e)}"
                    self.log_warning(error_msg)
                    
                    if attempt == retry_attempts:
                        execution_time = time.time() - start_time
                        return TaskResult(
                            task_id=task_id,
                            success=False,
                            result=None,
                            error=error_msg,
                            execution_time=execution_time,
                            timestamp=datetime.now().isoformat()
                        )
                    
                    # Wait before retry
                    await asyncio.sleep(0.5 * (attempt + 1))
    
    async def _execute_with_progress(
        self,
        task_coroutines: List[Awaitable],
        workflow_id: str,
        progress_callback: Optional[Callable]
    ) -> List[TaskResult]:
        """Execute tasks with progress tracking"""
        
        total_tasks = len(task_coroutines)
        completed_tasks = 0
        results = []
        
        # Create tasks
        tasks = [asyncio.create_task(coro) for coro in task_coroutines]
        
        # Monitor progress
        while tasks:
            # Wait for next task to complete
            done, pending = await asyncio.wait(
                tasks, 
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Process completed tasks
            for task in done:
                try:
                    result = await task
                    results.append(result)
                    completed_tasks += 1
                    
                    # Store result
                    if workflow_id in self.task_results:
                        self.task_results[workflow_id].append(result)
                    
                    # Call progress callback
                    if progress_callback:
                        try:
                            await progress_callback({
                                "workflow_id": workflow_id,
                                "completed": completed_tasks,
                                "total": total_tasks,
                                "progress": (completed_tasks / total_tasks) * 100,
                                "latest_result": result
                            })
                        except Exception as e:
                            self.log_warning(f"Progress callback failed: {e}")
                    
                except Exception as e:
                    self.log_error(f"Task execution error: {e}")
                    # Create error result
                    error_result = TaskResult(
                        task_id=f"unknown_{completed_tasks}",
                        success=False,
                        result=None,
                        error=str(e),
                        execution_time=0.0,
                        timestamp=datetime.now().isoformat()
                    )
                    results.append(error_result)
                    completed_tasks += 1
            
            # Update remaining tasks
            tasks = list(pending)
        
        return results
    
    async def _analyze_workflow_results(
        self,
        workflow: ParallelWorkflow,
        results: List[TaskResult],
        execution_time: float
    ) -> Dict[str, Any]:
        """Analyze workflow execution results"""
        
        total_tasks = len(results)
        successful_tasks = sum(1 for r in results if r.success)
        failed_tasks = total_tasks - successful_tasks
        success_rate = successful_tasks / total_tasks if total_tasks > 0 else 0.0
        
        # Check if workflow meets success criteria
        failure_rate = failed_tasks / total_tasks if total_tasks > 0 else 0.0
        workflow_success = failure_rate <= workflow.failure_threshold
        
        # Calculate performance metrics
        avg_task_time = sum(r.execution_time for r in results) / total_tasks if total_tasks > 0 else 0.0
        
        return {
            "workflow_id": workflow.workflow_id,
            "workflow_success": workflow_success,
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": success_rate,
            "failure_rate": failure_rate,
            "execution_time": execution_time,
            "average_task_time": avg_task_time,
            "task_results": results,
            "performance_metrics": {
                "throughput": total_tasks / execution_time if execution_time > 0 else 0,
                "efficiency": success_rate * (total_tasks / execution_time) if execution_time > 0 else 0
            }
        }
    
    def _update_execution_stats(
        self,
        workflow: ParallelWorkflow,
        results: List[TaskResult],
        execution_time: float
    ):
        """Update global execution statistics"""
        
        self.execution_stats["total_workflows"] += 1
        self.execution_stats["total_tasks"] += len(results)
        self.execution_stats["successful_tasks"] += sum(1 for r in results if r.success)
        self.execution_stats["failed_tasks"] += sum(1 for r in results if not r.success)
        
        # Update average execution time
        total_time = self.execution_stats["average_execution_time"] * (self.execution_stats["total_workflows"] - 1)
        self.execution_stats["average_execution_time"] = (total_time + execution_time) / self.execution_stats["total_workflows"]
    
    def _build_dependency_graph(self, tasks_with_deps: Dict[str, Dict[str, Any]]) -> Dict[str, List[str]]:
        """Build dependency graph for task execution"""
        
        graph = {}
        for task_id, task_info in tasks_with_deps.items():
            dependencies = task_info.get("dependencies", [])
            graph[task_id] = dependencies
        
        return graph
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return self.execution_stats.copy()
    
    def get_active_workflows(self) -> List[str]:
        """Get list of active workflow IDs"""
        return list(self.active_workflows.keys())
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel an active workflow"""
        try:
            if workflow_id in self.active_workflows:
                # Cancel all active tasks for this workflow
                for task_id, task in self.active_tasks.items():
                    if task_id.startswith(workflow_id):
                        task.cancel()
                
                # Remove from active workflows
                del self.active_workflows[workflow_id]
                
                self.log_info(f"Workflow cancelled: {workflow_id}")
                return True
            
            return False
            
        except Exception as e:
            self.log_error(f"Failed to cancel workflow {workflow_id}: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup parallel processor resources"""
        try:
            # Cancel all active workflows
            for workflow_id in list(self.active_workflows.keys()):
                await self.cancel_workflow(workflow_id)
            
            # Shutdown thread executor
            self.thread_executor.shutdown(wait=True)
            
            # Clear data structures
            self.task_results.clear()
            self.resource_semaphores.clear()
            self.active_tasks.clear()
            
            self.log_info("Parallel processor cleanup completed")
            
        except Exception as e:
            self.log_error(f"Parallel processor cleanup failed: {e}")


# Global parallel processor instance
parallel_processor = ParallelProcessor()
