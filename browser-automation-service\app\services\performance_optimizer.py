"""
Performance Optimizer
Advanced performance optimization and resource management for browser automation
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field
import psutil
import statistics

from app.core.logging import LoggerMixin


class OptimizationStrategy(Enum):
    """Performance optimization strategies"""
    MEMORY_OPTIMIZATION = "memory_optimization"
    CPU_OPTIMIZATION = "cpu_optimization"
    NETWORK_OPTIMIZATION = "network_optimization"
    CONCURRENCY_OPTIMIZATION = "concurrency_optimization"
    CACHE_OPTIMIZATION = "cache_optimization"
    RESOURCE_BALANCING = "resource_balancing"


class PerformanceMetric(Enum):
    """Performance metrics to track"""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    RESOURCE_UTILIZATION = "resource_utilization"
    QUEUE_LENGTH = "queue_length"
    SESSION_EFFICIENCY = "session_efficiency"


@dataclass
class PerformanceThreshold:
    """Performance threshold configuration"""
    metric: PerformanceMetric
    warning_threshold: float
    critical_threshold: float
    optimization_strategy: OptimizationStrategy
    auto_optimize: bool = True


@dataclass
class OptimizationResult:
    """Result of optimization operation"""
    strategy: OptimizationStrategy
    success: bool
    improvement_percentage: float
    metrics_before: Dict[str, float]
    metrics_after: Dict[str, float]
    execution_time: float
    recommendations: List[str] = field(default_factory=list)


class PerformanceOptimizer(LoggerMixin):
    """
    Advanced performance optimization system
    
    Features:
    - Real-time performance monitoring
    - Automatic optimization triggers
    - Resource usage optimization
    - Predictive scaling
    - Performance analytics
    - Bottleneck detection
    - Load balancing optimization
    - Memory and CPU management
    """
    
    def __init__(self):
        # Performance thresholds
        self.thresholds = {
            PerformanceMetric.RESPONSE_TIME: PerformanceThreshold(
                metric=PerformanceMetric.RESPONSE_TIME,
                warning_threshold=5.0,  # 5 seconds
                critical_threshold=10.0,  # 10 seconds
                optimization_strategy=OptimizationStrategy.CONCURRENCY_OPTIMIZATION
            ),
            PerformanceMetric.THROUGHPUT: PerformanceThreshold(
                metric=PerformanceMetric.THROUGHPUT,
                warning_threshold=10.0,  # 10 tasks/minute
                critical_threshold=5.0,   # 5 tasks/minute
                optimization_strategy=OptimizationStrategy.RESOURCE_BALANCING
            ),
            PerformanceMetric.ERROR_RATE: PerformanceThreshold(
                metric=PerformanceMetric.ERROR_RATE,
                warning_threshold=0.05,  # 5%
                critical_threshold=0.10,  # 10%
                optimization_strategy=OptimizationStrategy.MEMORY_OPTIMIZATION
            ),
            PerformanceMetric.RESOURCE_UTILIZATION: PerformanceThreshold(
                metric=PerformanceMetric.RESOURCE_UTILIZATION,
                warning_threshold=0.80,  # 80%
                critical_threshold=0.90,  # 90%
                optimization_strategy=OptimizationStrategy.CPU_OPTIMIZATION
            )
        }
        
        # Performance metrics history
        self.metrics_history: Dict[PerformanceMetric, List[Tuple[datetime, float]]] = {
            metric: [] for metric in PerformanceMetric
        }
        
        # Optimization history
        self.optimization_history: List[OptimizationResult] = []
        
        # Current performance state
        self.current_metrics: Dict[PerformanceMetric, float] = {}
        
        # Optimization settings
        self.optimization_config = {
            "auto_optimization_enabled": True,
            "optimization_interval": 60.0,  # 1 minute
            "metrics_retention_hours": 24,
            "min_optimization_interval": 300.0,  # 5 minutes between optimizations
            "performance_target_percentile": 95,
            "resource_utilization_target": 0.75
        }
        
        # Background tasks
        self._monitoring_task = None
        self._optimization_task = None
        
        self.log_info("Performance optimizer initialized")
    
    async def initialize(self):
        """Initialize performance optimizer"""
        try:
            self.log_info("Initializing performance optimizer")
            
            # Start background monitoring
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            # Start optimization loop
            if self.optimization_config["auto_optimization_enabled"]:
                self._optimization_task = asyncio.create_task(self._optimization_loop())
            
            self.log_info("Performance optimizer initialized successfully")
            
        except Exception as e:
            self.log_error(f"Performance optimizer initialization failed: {e}")
            raise
    
    async def record_metric(self, metric: PerformanceMetric, value: float):
        """Record a performance metric"""
        try:
            timestamp = datetime.now()
            
            # Add to history
            self.metrics_history[metric].append((timestamp, value))
            
            # Update current metrics
            self.current_metrics[metric] = value
            
            # Cleanup old metrics
            await self._cleanup_old_metrics()
            
            # Check thresholds
            await self._check_thresholds(metric, value)
            
        except Exception as e:
            self.log_error(f"Failed to record metric: {e}")
    
    async def optimize_performance(
        self,
        strategy: OptimizationStrategy,
        target_metrics: Optional[List[PerformanceMetric]] = None
    ) -> OptimizationResult:
        """
        Execute performance optimization
        
        Args:
            strategy: Optimization strategy to apply
            target_metrics: Optional specific metrics to optimize
            
        Returns:
            Optimization result
        """
        try:
            start_time = datetime.now()
            
            self.log_info(f"Starting performance optimization: {strategy.value}")
            
            # Capture metrics before optimization
            metrics_before = await self._capture_current_metrics()
            
            # Execute optimization strategy
            success = await self._execute_optimization_strategy(strategy, target_metrics)
            
            # Wait for metrics to stabilize
            await asyncio.sleep(5.0)
            
            # Capture metrics after optimization
            metrics_after = await self._capture_current_metrics()
            
            # Calculate improvement
            improvement = await self._calculate_improvement(metrics_before, metrics_after)
            
            # Generate recommendations
            recommendations = await self._generate_optimization_recommendations(
                strategy, metrics_before, metrics_after
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Create optimization result
            result = OptimizationResult(
                strategy=strategy,
                success=success,
                improvement_percentage=improvement,
                metrics_before=metrics_before,
                metrics_after=metrics_after,
                execution_time=execution_time,
                recommendations=recommendations
            )
            
            # Record optimization
            self.optimization_history.append(result)
            
            self.log_info(
                f"Optimization completed: {strategy.value}",
                success=success,
                improvement=f"{improvement:.1f}%",
                execution_time=execution_time
            )
            
            return result
            
        except Exception as e:
            self.log_error(f"Performance optimization failed: {e}")
            return OptimizationResult(
                strategy=strategy,
                success=False,
                improvement_percentage=0.0,
                metrics_before={},
                metrics_after={},
                execution_time=0.0,
                recommendations=[f"Optimization failed: {e}"]
            )
    
    async def get_performance_report(self, time_period: str = "1h") -> Dict[str, Any]:
        """
        Generate comprehensive performance report
        
        Args:
            time_period: Time period for analysis (1h, 6h, 24h)
            
        Returns:
            Performance report
        """
        try:
            # Parse time period
            if time_period == "1h":
                cutoff = datetime.now() - timedelta(hours=1)
            elif time_period == "6h":
                cutoff = datetime.now() - timedelta(hours=6)
            elif time_period == "24h":
                cutoff = datetime.now() - timedelta(hours=24)
            else:
                cutoff = datetime.now() - timedelta(hours=1)
            
            # Analyze metrics
            metrics_analysis = await self._analyze_metrics(cutoff)
            
            # Analyze optimizations
            optimization_analysis = await self._analyze_optimizations(cutoff)
            
            # Detect bottlenecks
            bottlenecks = await self._detect_bottlenecks()
            
            # Generate recommendations
            recommendations = await self._generate_performance_recommendations()
            
            # Get current resource usage
            resource_usage = await self._get_resource_usage()
            
            return {
                "time_period": time_period,
                "generated_at": datetime.now().isoformat(),
                "current_metrics": self.current_metrics,
                "metrics_analysis": metrics_analysis,
                "optimization_analysis": optimization_analysis,
                "bottlenecks": bottlenecks,
                "recommendations": recommendations,
                "resource_usage": resource_usage,
                "performance_score": await self._calculate_performance_score()
            }
            
        except Exception as e:
            self.log_error(f"Performance report generation failed: {e}")
            return {"error": str(e)}
    
    async def predict_performance_issues(self) -> List[Dict[str, Any]]:
        """
        Predict potential performance issues using trend analysis
        
        Returns:
            List of predicted issues
        """
        try:
            predictions = []
            
            for metric, history in self.metrics_history.items():
                if len(history) < 10:  # Need sufficient data
                    continue
                
                # Analyze trend
                recent_values = [value for _, value in history[-10:]]
                trend = await self._calculate_trend(recent_values)
                
                # Predict future value
                predicted_value = recent_values[-1] + (trend * 5)  # 5 periods ahead
                
                # Check if prediction exceeds thresholds
                threshold = self.thresholds.get(metric)
                if threshold:
                    if predicted_value > threshold.critical_threshold:
                        predictions.append({
                            "metric": metric.value,
                            "current_value": recent_values[-1],
                            "predicted_value": predicted_value,
                            "threshold": threshold.critical_threshold,
                            "severity": "critical",
                            "estimated_time": "5-10 minutes",
                            "recommended_action": threshold.optimization_strategy.value
                        })
                    elif predicted_value > threshold.warning_threshold:
                        predictions.append({
                            "metric": metric.value,
                            "current_value": recent_values[-1],
                            "predicted_value": predicted_value,
                            "threshold": threshold.warning_threshold,
                            "severity": "warning",
                            "estimated_time": "10-15 minutes",
                            "recommended_action": "monitor_closely"
                        })
            
            return predictions
            
        except Exception as e:
            self.log_error(f"Performance prediction failed: {e}")
            return []
    
    async def shutdown(self):
        """Shutdown performance optimizer"""
        try:
            self.log_info("Shutting down performance optimizer")
            
            # Cancel background tasks
            if self._monitoring_task:
                self._monitoring_task.cancel()
            if self._optimization_task:
                self._optimization_task.cancel()
            
            self.log_info("Performance optimizer shutdown completed")
            
        except Exception as e:
            self.log_error(f"Performance optimizer shutdown failed: {e}")
    
    # Helper methods
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while True:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds
                await self._collect_system_metrics()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Monitoring loop error: {e}")
    
    async def _optimization_loop(self):
        """Background optimization loop"""
        while True:
            try:
                await asyncio.sleep(self.optimization_config["optimization_interval"])
                await self._auto_optimize()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Optimization loop error: {e}")
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            process = psutil.Process()
            
            # CPU utilization
            cpu_percent = process.cpu_percent()
            await self.record_metric(PerformanceMetric.RESOURCE_UTILIZATION, cpu_percent / 100.0)
            
            # Memory utilization
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # Calculate additional metrics based on system state
            # This would integrate with session manager and other components
            
        except Exception as e:
            self.log_error(f"System metrics collection failed: {e}")
    
    async def _check_thresholds(self, metric: PerformanceMetric, value: float):
        """Check if metric exceeds thresholds"""
        try:
            threshold = self.thresholds.get(metric)
            if not threshold:
                return
            
            if value > threshold.critical_threshold:
                self.log_warning(
                    f"Critical threshold exceeded: {metric.value}",
                    value=value,
                    threshold=threshold.critical_threshold
                )
                
                if threshold.auto_optimize:
                    await self.optimize_performance(threshold.optimization_strategy, [metric])
                    
            elif value > threshold.warning_threshold:
                self.log_info(
                    f"Warning threshold exceeded: {metric.value}",
                    value=value,
                    threshold=threshold.warning_threshold
                )
                
        except Exception as e:
            self.log_error(f"Threshold check failed: {e}")
    
    async def _cleanup_old_metrics(self):
        """Clean up old metrics data"""
        try:
            cutoff = datetime.now() - timedelta(hours=self.optimization_config["metrics_retention_hours"])
            
            for metric, history in self.metrics_history.items():
                # Remove old entries
                self.metrics_history[metric] = [
                    (timestamp, value) for timestamp, value in history
                    if timestamp > cutoff
                ]
                
        except Exception as e:
            self.log_error(f"Metrics cleanup failed: {e}")
    
    async def _execute_optimization_strategy(
        self,
        strategy: OptimizationStrategy,
        target_metrics: Optional[List[PerformanceMetric]]
    ) -> bool:
        """Execute specific optimization strategy"""
        try:
            if strategy == OptimizationStrategy.MEMORY_OPTIMIZATION:
                return await self._optimize_memory()
            elif strategy == OptimizationStrategy.CPU_OPTIMIZATION:
                return await self._optimize_cpu()
            elif strategy == OptimizationStrategy.CONCURRENCY_OPTIMIZATION:
                return await self._optimize_concurrency()
            elif strategy == OptimizationStrategy.RESOURCE_BALANCING:
                return await self._optimize_resource_balancing()
            else:
                self.log_warning(f"Unknown optimization strategy: {strategy.value}")
                return False
                
        except Exception as e:
            self.log_error(f"Optimization strategy execution failed: {e}")
            return False
    
    async def _optimize_memory(self) -> bool:
        """Optimize memory usage"""
        try:
            # Trigger garbage collection
            import gc
            gc.collect()
            
            # Additional memory optimization would go here
            # e.g., clearing caches, optimizing session pool
            
            return True
            
        except Exception as e:
            self.log_error(f"Memory optimization failed: {e}")
            return False
    
    async def _optimize_cpu(self) -> bool:
        """Optimize CPU usage"""
        try:
            # CPU optimization strategies would go here
            # e.g., adjusting concurrency limits, optimizing algorithms
            
            return True
            
        except Exception as e:
            self.log_error(f"CPU optimization failed: {e}")
            return False
    
    async def _optimize_concurrency(self) -> bool:
        """Optimize concurrency settings"""
        try:
            # Concurrency optimization would go here
            # e.g., adjusting session pool size, task queue limits
            
            return True
            
        except Exception as e:
            self.log_error(f"Concurrency optimization failed: {e}")
            return False
    
    async def _optimize_resource_balancing(self) -> bool:
        """Optimize resource balancing"""
        try:
            # Resource balancing optimization would go here
            # e.g., load balancing, resource allocation
            
            return True
            
        except Exception as e:
            self.log_error(f"Resource balancing optimization failed: {e}")
            return False
    
    async def _capture_current_metrics(self) -> Dict[str, float]:
        """Capture current performance metrics"""
        try:
            return {
                metric.value: value
                for metric, value in self.current_metrics.items()
            }
        except Exception:
            return {}
    
    async def _calculate_improvement(
        self,
        metrics_before: Dict[str, float],
        metrics_after: Dict[str, float]
    ) -> float:
        """Calculate performance improvement percentage"""
        try:
            if not metrics_before or not metrics_after:
                return 0.0
            
            improvements = []
            
            for metric_name in metrics_before.keys():
                if metric_name in metrics_after:
                    before = metrics_before[metric_name]
                    after = metrics_after[metric_name]
                    
                    if before > 0:
                        improvement = ((before - after) / before) * 100
                        improvements.append(improvement)
            
            return statistics.mean(improvements) if improvements else 0.0
            
        except Exception:
            return 0.0
    
    async def _auto_optimize(self):
        """Automatic optimization based on current metrics"""
        try:
            # Check if optimization is needed
            for metric, value in self.current_metrics.items():
                threshold = self.thresholds.get(metric)
                if threshold and threshold.auto_optimize:
                    if value > threshold.critical_threshold:
                        await self.optimize_performance(threshold.optimization_strategy, [metric])
                        break  # Only one optimization per cycle
                        
        except Exception as e:
            self.log_error(f"Auto optimization failed: {e}")


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
