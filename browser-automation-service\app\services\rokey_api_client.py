"""
RouKey API Client
Handles communication with the main RouKey system for role routing and configuration
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx

from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class RouKeyAPIClient(LoggerMixin):
    """
    Client for communicating with RouKey's main API system
    
    Features:
    - User configuration retrieval
    - Role classification requests
    - Routing strategy management
    - Real-time role consultation
    - Performance metrics reporting
    - Cost tracking and optimization
    """
    
    def __init__(self):
        self.base_url = settings.ROKEY_API_BASE_URL
        self.api_key = settings.ROKEY_API_KEY
        self.http_client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "RouKey-BrowserAutomation/1.0"
            }
        )
        
        # Request tracking
        self.request_count = 0
        self.error_count = 0
        self.last_request_time = None
        
        self.log_info("RouKey API client initialized")
    
    async def get_user_configuration(self, user_id: str) -> Dict[str, Any]:
        """
        Get user's complete configuration including roles and routing preferences
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary containing user configuration
        """
        try:
            response = await self._make_request(
                "GET",
                f"/api/users/{user_id}/config",
                timeout=10.0
            )
            
            if response.get("success"):
                config = response.get("data", {})
                
                self.log_info(f"User configuration retrieved: {user_id}")
                
                return {
                    "user_id": user_id,
                    "subscription_tier": config.get("subscription_tier", "free"),
                    "available_roles": config.get("roles", []),
                    "routing_strategy": config.get("routing_strategy", "complex_routing"),
                    "api_keys": config.get("api_keys", {}),
                    "preferences": config.get("preferences", {}),
                    "usage_limits": config.get("usage_limits", {}),
                    "browsing_enabled": config.get("browsing_automation", {}).get("enabled", False),
                    "browsing_quota": config.get("browsing_automation", {}).get("monthly_quota", 0)
                }
            else:
                raise BrowserAutomationException(f"Failed to get user config: {response.get('error')}")
                
        except Exception as e:
            self.log_error(f"User configuration retrieval failed: {e}")
            # Return default configuration
            return {
                "user_id": user_id,
                "subscription_tier": "free",
                "available_roles": ["web_automation", "task_executor"],
                "routing_strategy": "sequential",
                "api_keys": {},
                "preferences": {},
                "usage_limits": {"browsing_tasks": 5},
                "browsing_enabled": True,
                "browsing_quota": 5
            }
    
    async def classify_task_with_rokey(
        self,
        task_description: str,
        user_id: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Use RouKey's classification system to analyze the task
        
        Args:
            task_description: Task to classify
            user_id: User making the request
            context: Additional context for classification
            
        Returns:
            Dictionary containing classification results
        """
        try:
            payload = {
                "task": task_description,
                "user_id": user_id,
                "context": context or {},
                "service": "browser_automation",
                "timestamp": datetime.now().isoformat()
            }
            
            response = await self._make_request(
                "POST",
                "/api/classify/task",
                data=payload,
                timeout=15.0
            )
            
            if response.get("success"):
                classification = response.get("data", {})
                
                self.log_info(
                    "Task classification completed",
                    task_type=classification.get("task_type"),
                    confidence=classification.get("confidence")
                )
                
                return {
                    "task_type": classification.get("task_type", "general"),
                    "complexity": classification.get("complexity", "moderate"),
                    "confidence": classification.get("confidence", 0.7),
                    "recommended_roles": classification.get("recommended_roles", []),
                    "routing_strategy": classification.get("routing_strategy", "complex_routing"),
                    "estimated_cost": classification.get("estimated_cost", 0.01),
                    "estimated_duration": classification.get("estimated_duration", 30),
                    "requires_verification": classification.get("requires_verification", False)
                }
            else:
                raise BrowserAutomationException(f"Task classification failed: {response.get('error')}")
                
        except Exception as e:
            self.log_error(f"Task classification failed: {e}")
            # Return fallback classification
            return {
                "task_type": "browser_automation",
                "complexity": "moderate",
                "confidence": 0.5,
                "recommended_roles": ["web_automation"],
                "routing_strategy": "sequential",
                "estimated_cost": 0.01,
                "estimated_duration": 30,
                "requires_verification": False
            }
    
    async def get_role_configuration(
        self,
        role: str,
        user_id: str,
        routing_strategy: str = "complex_routing"
    ) -> Dict[str, Any]:
        """
        Get LLM configuration for a specific role
        
        Args:
            role: Role name
            user_id: User identifier
            routing_strategy: Routing strategy to apply
            
        Returns:
            Dictionary containing role configuration
        """
        try:
            params = {
                "role": role,
                "user_id": user_id,
                "routing_strategy": routing_strategy,
                "service": "browser_automation"
            }
            
            response = await self._make_request(
                "GET",
                "/api/roles/config",
                params=params,
                timeout=10.0
            )
            
            if response.get("success"):
                config = response.get("data", {})
                
                self.log_info(f"Role configuration retrieved: {role}")
                
                return {
                    "provider": config.get("provider", "openai"),
                    "model": config.get("model", "gpt-4o"),
                    "api_key": config.get("api_key"),
                    "temperature": config.get("temperature", 0.7),
                    "max_tokens": config.get("max_tokens", 2000),
                    "role": role,
                    "routing_strategy": routing_strategy,
                    "cost_per_token": config.get("cost_per_token", 0.00003),
                    "rate_limit": config.get("rate_limit", 60),
                    "timeout": config.get("timeout", 30)
                }
            else:
                raise BrowserAutomationException(f"Role config failed: {response.get('error')}")
                
        except Exception as e:
            self.log_error(f"Role configuration retrieval failed: {e}")
            # Return fallback configuration
            return {
                "provider": "openai",
                "model": "gpt-3.5-turbo",
                "api_key": settings.OPENAI_API_KEY,
                "temperature": 0.7,
                "max_tokens": 1500,
                "role": role,
                "routing_strategy": routing_strategy,
                "cost_per_token": 0.000002,
                "rate_limit": 60,
                "timeout": 30
            }
    
    async def request_role_consultation(
        self,
        current_role: str,
        problem_description: str,
        user_id: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Request consultation from another role when facing problems
        
        Args:
            current_role: Role currently handling the task
            problem_description: Description of the problem
            user_id: User identifier
            context: Additional context
            
        Returns:
            Dictionary containing consultation recommendation
        """
        try:
            payload = {
                "current_role": current_role,
                "problem": problem_description,
                "user_id": user_id,
                "context": context or {},
                "service": "browser_automation",
                "timestamp": datetime.now().isoformat()
            }
            
            response = await self._make_request(
                "POST",
                "/api/roles/consult",
                data=payload,
                timeout=15.0
            )
            
            if response.get("success"):
                consultation = response.get("data", {})
                
                self.log_info(
                    "Role consultation completed",
                    consulting_role=consultation.get("consulting_role")
                )
                
                return {
                    "consulting_role": consultation.get("consulting_role"),
                    "consultation_strategy": consultation.get("strategy", "expert_help"),
                    "estimated_cost": consultation.get("estimated_cost", 0.02),
                    "confidence": consultation.get("confidence", 0.8),
                    "reasoning": consultation.get("reasoning", ""),
                    "alternative_roles": consultation.get("alternative_roles", [])
                }
            else:
                raise BrowserAutomationException(f"Role consultation failed: {response.get('error')}")
                
        except Exception as e:
            self.log_error(f"Role consultation failed: {e}")
            # Return fallback consultation
            return {
                "consulting_role": "expert_assistant",
                "consultation_strategy": "fallback_help",
                "estimated_cost": 0.02,
                "confidence": 0.6,
                "reasoning": "Fallback consultation due to API error",
                "alternative_roles": ["research_assistant", "verification_agent"]
            }
    
    async def report_execution_metrics(
        self,
        user_id: str,
        execution_data: Dict[str, Any]
    ) -> bool:
        """
        Report execution metrics back to RouKey for analytics
        
        Args:
            user_id: User identifier
            execution_data: Execution metrics and results
            
        Returns:
            True if reporting was successful
        """
        try:
            payload = {
                "user_id": user_id,
                "service": "browser_automation",
                "execution_data": execution_data,
                "timestamp": datetime.now().isoformat()
            }
            
            response = await self._make_request(
                "POST",
                "/api/metrics/execution",
                data=payload,
                timeout=10.0
            )
            
            if response.get("success"):
                self.log_info("Execution metrics reported successfully")
                return True
            else:
                self.log_warning(f"Metrics reporting failed: {response.get('error')}")
                return False
                
        except Exception as e:
            self.log_warning(f"Metrics reporting failed: {e}")
            return False
    
    async def update_usage_quota(
        self,
        user_id: str,
        quota_type: str,
        amount_used: int
    ) -> Dict[str, Any]:
        """
        Update user's usage quota
        
        Args:
            user_id: User identifier
            quota_type: Type of quota (browsing_tasks, api_calls, etc.)
            amount_used: Amount to deduct from quota
            
        Returns:
            Dictionary containing updated quota information
        """
        try:
            payload = {
                "user_id": user_id,
                "quota_type": quota_type,
                "amount_used": amount_used,
                "service": "browser_automation",
                "timestamp": datetime.now().isoformat()
            }
            
            response = await self._make_request(
                "POST",
                "/api/users/quota/update",
                data=payload,
                timeout=10.0
            )
            
            if response.get("success"):
                quota_info = response.get("data", {})
                
                self.log_info(f"Quota updated for user {user_id}")
                
                return {
                    "quota_type": quota_type,
                    "remaining_quota": quota_info.get("remaining", 0),
                    "total_quota": quota_info.get("total", 0),
                    "reset_date": quota_info.get("reset_date"),
                    "quota_exceeded": quota_info.get("exceeded", False)
                }
            else:
                raise BrowserAutomationException(f"Quota update failed: {response.get('error')}")
                
        except Exception as e:
            self.log_error(f"Quota update failed: {e}")
            return {
                "quota_type": quota_type,
                "remaining_quota": 0,
                "total_quota": 0,
                "quota_exceeded": True
            }
    
    async def get_routing_strategy_config(
        self,
        user_id: str,
        strategy: str
    ) -> Dict[str, Any]:
        """
        Get configuration for a specific routing strategy
        
        Args:
            user_id: User identifier
            strategy: Routing strategy name
            
        Returns:
            Dictionary containing strategy configuration
        """
        try:
            params = {
                "user_id": user_id,
                "strategy": strategy,
                "service": "browser_automation"
            }
            
            response = await self._make_request(
                "GET",
                "/api/routing/strategy",
                params=params,
                timeout=10.0
            )
            
            if response.get("success"):
                config = response.get("data", {})
                
                return {
                    "strategy": strategy,
                    "enabled": config.get("enabled", True),
                    "parameters": config.get("parameters", {}),
                    "fallback_strategy": config.get("fallback_strategy", "sequential"),
                    "cost_optimization": config.get("cost_optimization", False),
                    "ab_testing_config": config.get("ab_testing", {})
                }
            else:
                raise BrowserAutomationException(f"Strategy config failed: {response.get('error')}")
                
        except Exception as e:
            self.log_error(f"Routing strategy config failed: {e}")
            return {
                "strategy": strategy,
                "enabled": True,
                "parameters": {},
                "fallback_strategy": "sequential",
                "cost_optimization": False,
                "ab_testing_config": {}
            }
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Dict[str, Any] = None,
        params: Dict[str, Any] = None,
        timeout: float = 30.0
    ) -> Dict[str, Any]:
        """
        Make HTTP request to RouKey API
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            timeout: Request timeout
            
        Returns:
            Dictionary containing response data
        """
        try:
            url = f"{self.base_url}{endpoint}"
            
            # Track request
            self.request_count += 1
            self.last_request_time = datetime.now()
            
            # Make request
            if method.upper() == "GET":
                response = await self.http_client.get(url, params=params, timeout=timeout)
            elif method.upper() == "POST":
                response = await self.http_client.post(url, json=data, params=params, timeout=timeout)
            elif method.upper() == "PUT":
                response = await self.http_client.put(url, json=data, params=params, timeout=timeout)
            elif method.upper() == "DELETE":
                response = await self.http_client.delete(url, params=params, timeout=timeout)
            else:
                raise BrowserAutomationException(f"Unsupported HTTP method: {method}")
            
            # Parse response
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return {"success": False, "error": "Endpoint not found"}
            elif response.status_code == 401:
                return {"success": False, "error": "Authentication failed"}
            elif response.status_code == 429:
                return {"success": False, "error": "Rate limit exceeded"}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except httpx.TimeoutException:
            self.error_count += 1
            return {"success": False, "error": "Request timeout"}
        except Exception as e:
            self.error_count += 1
            self.log_error(f"API request failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_client_stats(self) -> Dict[str, Any]:
        """Get client statistics"""
        return {
            "total_requests": self.request_count,
            "error_count": self.error_count,
            "error_rate": (self.error_count / self.request_count * 100) if self.request_count > 0 else 0,
            "last_request": self.last_request_time.isoformat() if self.last_request_time else None,
            "base_url": self.base_url
        }
    
    async def cleanup(self):
        """Cleanup API client resources"""
        try:
            await self.http_client.aclose()
            self.log_info("RouKey API client cleanup completed")
        except Exception as e:
            self.log_error(f"API client cleanup failed: {e}")


# Global RouKey API client instance
rokey_api_client = RouKeyAPIClient()
