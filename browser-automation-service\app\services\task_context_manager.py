"""
Task Context Manager
Maintains context and state across all agents in browser automation workflows
"""

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass, asdict, field
from enum import Enum
import uuid

from app.models.browser_automation import <PERSON>rowser<PERSON><PERSON>mationState
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class ContextScope(Enum):
    """Context scope levels"""
    GLOBAL = "global"          # Shared across all workflows
    WORKFLOW = "workflow"      # Shared within a workflow
    AGENT = "agent"           # Specific to an agent
    TASK = "task"             # Specific to a task
    SESSION = "session"       # Specific to a browser session


@dataclass
class ContextEntry:
    """Individual context entry"""
    key: str
    value: Any
    scope: ContextScope
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    access_count: int = 0
    
    def is_expired(self) -> bool:
        """Check if context entry has expired"""
        return self.expires_at is not None and datetime.now() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "key": self.key,
            "value": self.value,
            "scope": self.scope.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "metadata": self.metadata,
            "access_count": self.access_count
        }


@dataclass
class AgentContext:
    """Context specific to an agent"""
    agent_id: str
    agent_role: str
    workflow_id: str
    session_id: str
    current_task_id: Optional[str] = None
    execution_state: Dict[str, Any] = field(default_factory=dict)
    memory_context: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    error_history: List[Dict[str, Any]] = field(default_factory=list)
    last_activity: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "agent_id": self.agent_id,
            "agent_role": self.agent_role,
            "workflow_id": self.workflow_id,
            "session_id": self.session_id,
            "current_task_id": self.current_task_id,
            "execution_state": self.execution_state,
            "memory_context": self.memory_context,
            "performance_metrics": self.performance_metrics,
            "error_history": self.error_history,
            "last_activity": self.last_activity.isoformat()
        }


class TaskContextManager(LoggerMixin):
    """
    Comprehensive context management system for browser automation
    
    Features:
    - Multi-scope context storage (global, workflow, agent, task, session)
    - Context inheritance and propagation
    - Automatic context cleanup and expiration
    - Context versioning and history
    - Cross-agent context sharing
    - Context synchronization and locking
    - Context analytics and insights
    - Context backup and recovery
    """
    
    def __init__(self):
        # Context storage by scope
        self.global_context: Dict[str, ContextEntry] = {}
        self.workflow_contexts: Dict[str, Dict[str, ContextEntry]] = {}
        self.agent_contexts: Dict[str, AgentContext] = {}
        self.task_contexts: Dict[str, Dict[str, ContextEntry]] = {}
        self.session_contexts: Dict[str, Dict[str, ContextEntry]] = {}
        
        # Context relationships
        self.context_dependencies: Dict[str, Set[str]] = {}
        self.context_subscribers: Dict[str, Set[str]] = {}
        
        # Context versioning
        self.context_versions: Dict[str, List[Dict[str, Any]]] = {}
        self.context_locks: Dict[str, asyncio.Lock] = {}
        
        # Cleanup and maintenance
        self.cleanup_interval = 300  # 5 minutes
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Context analytics
        self.access_patterns: Dict[str, List[Dict[str, Any]]] = {}
        self.context_metrics: Dict[str, Any] = {}
        
        self.log_info("Task context manager initialized")
    
    async def initialize(self):
        """Initialize the context manager"""
        try:
            # Start cleanup task
            self.cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
            self.log_info("Task context manager started")
            
        except Exception as e:
            self.log_error(f"Failed to initialize context manager: {e}")
            raise BrowserAutomationException(f"Context manager initialization failed: {e}")
    
    async def set_context(
        self,
        key: str,
        value: Any,
        scope: ContextScope,
        scope_id: str,
        ttl_seconds: Optional[int] = None,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        Set a context value with specified scope
        
        Args:
            key: Context key
            value: Context value
            scope: Context scope level
            scope_id: ID for the scope (workflow_id, agent_id, etc.)
            ttl_seconds: Time to live in seconds
            metadata: Additional metadata
            
        Returns:
            True if context was set successfully
        """
        try:
            # Calculate expiration time
            expires_at = None
            if ttl_seconds:
                expires_at = datetime.now() + timedelta(seconds=ttl_seconds)
            
            # Create context entry
            context_entry = ContextEntry(
                key=key,
                value=value,
                scope=scope,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                expires_at=expires_at,
                metadata=metadata or {}
            )
            
            # Store in appropriate scope
            context_key = f"{scope.value}:{scope_id}:{key}"
            
            # Get or create lock for this context
            if context_key not in self.context_locks:
                self.context_locks[context_key] = asyncio.Lock()
            
            async with self.context_locks[context_key]:
                # Store context based on scope
                if scope == ContextScope.GLOBAL:
                    self.global_context[key] = context_entry
                elif scope == ContextScope.WORKFLOW:
                    if scope_id not in self.workflow_contexts:
                        self.workflow_contexts[scope_id] = {}
                    self.workflow_contexts[scope_id][key] = context_entry
                elif scope == ContextScope.TASK:
                    if scope_id not in self.task_contexts:
                        self.task_contexts[scope_id] = {}
                    self.task_contexts[scope_id][key] = context_entry
                elif scope == ContextScope.SESSION:
                    if scope_id not in self.session_contexts:
                        self.session_contexts[scope_id] = {}
                    self.session_contexts[scope_id][key] = context_entry
                
                # Store version history
                await self._store_context_version(context_key, context_entry)
                
                # Update metrics
                await self._update_context_metrics("set", scope, key)
            
            self.log_debug(f"Context set: {context_key}")
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to set context: {e}")
            return False
    
    async def get_context(
        self,
        key: str,
        scope: ContextScope,
        scope_id: str,
        default: Any = None,
        inherit: bool = True
    ) -> Any:
        """
        Get a context value with optional inheritance
        
        Args:
            key: Context key
            scope: Context scope level
            scope_id: ID for the scope
            default: Default value if not found
            inherit: Whether to check parent scopes
            
        Returns:
            Context value or default
        """
        try:
            context_key = f"{scope.value}:{scope_id}:{key}"
            
            # Get lock for this context
            if context_key not in self.context_locks:
                self.context_locks[context_key] = asyncio.Lock()
            
            async with self.context_locks[context_key]:
                # Try to get from specified scope
                context_entry = await self._get_context_entry(key, scope, scope_id)
                
                if context_entry and not context_entry.is_expired():
                    context_entry.access_count += 1
                    context_entry.updated_at = datetime.now()
                    
                    # Record access pattern
                    await self._record_access_pattern(context_key, scope_id)
                    
                    # Update metrics
                    await self._update_context_metrics("get", scope, key)
                    
                    return context_entry.value
                
                # Try inheritance if enabled
                if inherit:
                    inherited_value = await self._get_inherited_context(key, scope, scope_id)
                    if inherited_value is not None:
                        return inherited_value
                
                return default
                
        except Exception as e:
            self.log_error(f"Failed to get context: {e}")
            return default
    
    async def create_agent_context(
        self,
        agent_id: str,
        agent_role: str,
        workflow_id: str,
        session_id: str
    ) -> AgentContext:
        """
        Create context for a new agent
        
        Args:
            agent_id: Unique agent identifier
            agent_role: Role of the agent
            workflow_id: Workflow the agent belongs to
            session_id: Browser session ID
            
        Returns:
            AgentContext object
        """
        try:
            agent_context = AgentContext(
                agent_id=agent_id,
                agent_role=agent_role,
                workflow_id=workflow_id,
                session_id=session_id
            )
            
            self.agent_contexts[agent_id] = agent_context
            
            # Initialize agent-specific context storage
            await self.set_context(
                key="agent_info",
                value=agent_context.to_dict(),
                scope=ContextScope.AGENT,
                scope_id=agent_id
            )
            
            self.log_info(f"Agent context created: {agent_id}", role=agent_role)
            
            return agent_context
            
        except Exception as e:
            self.log_error(f"Failed to create agent context: {e}")
            raise BrowserAutomationException(f"Agent context creation failed: {e}")
    
    async def update_agent_context(
        self,
        agent_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """
        Update agent context with new information
        
        Args:
            agent_id: Agent identifier
            updates: Dictionary of updates to apply
            
        Returns:
            True if update was successful
        """
        try:
            if agent_id not in self.agent_contexts:
                self.log_warning(f"Agent context not found: {agent_id}")
                return False
            
            agent_context = self.agent_contexts[agent_id]
            
            # Apply updates
            for key, value in updates.items():
                if hasattr(agent_context, key):
                    setattr(agent_context, key, value)
                else:
                    # Store in execution_state if not a direct attribute
                    agent_context.execution_state[key] = value
            
            # Update last activity
            agent_context.last_activity = datetime.now()
            
            # Update stored context
            await self.set_context(
                key="agent_info",
                value=agent_context.to_dict(),
                scope=ContextScope.AGENT,
                scope_id=agent_id
            )
            
            self.log_debug(f"Agent context updated: {agent_id}")
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to update agent context: {e}")
            return False
    
    async def get_workflow_context(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get all context for a workflow
        
        Args:
            workflow_id: Workflow identifier
            
        Returns:
            Dictionary containing all workflow context
        """
        try:
            workflow_context = {}
            
            # Get workflow-specific context
            if workflow_id in self.workflow_contexts:
                for key, entry in self.workflow_contexts[workflow_id].items():
                    if not entry.is_expired():
                        workflow_context[key] = entry.value
            
            # Get global context
            for key, entry in self.global_context.items():
                if not entry.is_expired() and key not in workflow_context:
                    workflow_context[key] = entry.value
            
            # Get agent contexts for this workflow
            workflow_agents = {}
            for agent_id, agent_context in self.agent_contexts.items():
                if agent_context.workflow_id == workflow_id:
                    workflow_agents[agent_id] = agent_context.to_dict()
            
            if workflow_agents:
                workflow_context["agents"] = workflow_agents
            
            return workflow_context
            
        except Exception as e:
            self.log_error(f"Failed to get workflow context: {e}")
            return {}
    
    async def share_context_between_agents(
        self,
        source_agent_id: str,
        target_agent_id: str,
        context_keys: List[str]
    ) -> bool:
        """
        Share specific context between agents
        
        Args:
            source_agent_id: Agent sharing the context
            target_agent_id: Agent receiving the context
            context_keys: List of context keys to share
            
        Returns:
            True if sharing was successful
        """
        try:
            if source_agent_id not in self.agent_contexts or target_agent_id not in self.agent_contexts:
                return False
            
            source_context = self.agent_contexts[source_agent_id]
            target_context = self.agent_contexts[target_agent_id]
            
            # Share specified context keys
            for key in context_keys:
                if key in source_context.execution_state:
                    target_context.execution_state[f"shared_{key}"] = source_context.execution_state[key]
                    
                    # Record the sharing relationship
                    sharing_key = f"agent:{source_agent_id}:{key}"
                    if sharing_key not in self.context_subscribers:
                        self.context_subscribers[sharing_key] = set()
                    self.context_subscribers[sharing_key].add(target_agent_id)
            
            # Update target agent context
            await self.update_agent_context(target_agent_id, {})
            
            self.log_info(f"Context shared: {source_agent_id} -> {target_agent_id}", keys=context_keys)
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to share context between agents: {e}")
            return False
    
    async def get_context_history(
        self,
        key: str,
        scope: ContextScope,
        scope_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get version history for a context key
        
        Args:
            key: Context key
            scope: Context scope
            scope_id: Scope identifier
            limit: Maximum number of versions to return
            
        Returns:
            List of context versions
        """
        try:
            context_key = f"{scope.value}:{scope_id}:{key}"
            
            if context_key in self.context_versions:
                versions = self.context_versions[context_key]
                return versions[-limit:] if len(versions) > limit else versions
            
            return []
            
        except Exception as e:
            self.log_error(f"Failed to get context history: {e}")
            return []
    
    async def cleanup_expired_context(self) -> int:
        """
        Clean up expired context entries
        
        Returns:
            Number of entries cleaned up
        """
        try:
            cleaned_count = 0
            
            # Clean global context
            expired_keys = [
                key for key, entry in self.global_context.items()
                if entry.is_expired()
            ]
            for key in expired_keys:
                del self.global_context[key]
                cleaned_count += 1
            
            # Clean workflow contexts
            for workflow_id, context_dict in self.workflow_contexts.items():
                expired_keys = [
                    key for key, entry in context_dict.items()
                    if entry.is_expired()
                ]
                for key in expired_keys:
                    del context_dict[key]
                    cleaned_count += 1
            
            # Clean task contexts
            for task_id, context_dict in self.task_contexts.items():
                expired_keys = [
                    key for key, entry in context_dict.items()
                    if entry.is_expired()
                ]
                for key in expired_keys:
                    del context_dict[key]
                    cleaned_count += 1
            
            # Clean session contexts
            for session_id, context_dict in self.session_contexts.items():
                expired_keys = [
                    key for key, entry in context_dict.items()
                    if entry.is_expired()
                ]
                for key in expired_keys:
                    del context_dict[key]
                    cleaned_count += 1
            
            # Clean inactive agent contexts
            inactive_agents = [
                agent_id for agent_id, agent_context in self.agent_contexts.items()
                if (datetime.now() - agent_context.last_activity).total_seconds() > 3600  # 1 hour
            ]
            for agent_id in inactive_agents:
                del self.agent_contexts[agent_id]
                cleaned_count += 1
            
            if cleaned_count > 0:
                self.log_info(f"Cleaned up {cleaned_count} expired context entries")
            
            return cleaned_count
            
        except Exception as e:
            self.log_error(f"Failed to cleanup expired context: {e}")
            return 0
    
    async def get_context_analytics(self) -> Dict[str, Any]:
        """
        Get analytics about context usage
        
        Returns:
            Dictionary containing context analytics
        """
        try:
            analytics = {
                "total_global_context": len(self.global_context),
                "total_workflow_contexts": len(self.workflow_contexts),
                "total_agent_contexts": len(self.agent_contexts),
                "total_task_contexts": len(self.task_contexts),
                "total_session_contexts": len(self.session_contexts),
                "context_locks": len(self.context_locks),
                "context_subscribers": len(self.context_subscribers),
                "access_patterns": len(self.access_patterns),
                "most_accessed_contexts": [],
                "context_by_scope": {}
            }
            
            # Calculate context by scope
            for scope in ContextScope:
                analytics["context_by_scope"][scope.value] = 0
            
            # Count global context
            analytics["context_by_scope"]["global"] = len(self.global_context)
            
            # Count workflow contexts
            for workflow_contexts in self.workflow_contexts.values():
                analytics["context_by_scope"]["workflow"] += len(workflow_contexts)
            
            # Count task contexts
            for task_contexts in self.task_contexts.values():
                analytics["context_by_scope"]["task"] += len(task_contexts)
            
            # Count session contexts
            for session_contexts in self.session_contexts.values():
                analytics["context_by_scope"]["session"] += len(session_contexts)
            
            # Find most accessed contexts
            access_counts = []
            
            for key, entry in self.global_context.items():
                access_counts.append((f"global:{key}", entry.access_count))
            
            for workflow_id, contexts in self.workflow_contexts.items():
                for key, entry in contexts.items():
                    access_counts.append((f"workflow:{workflow_id}:{key}", entry.access_count))
            
            # Sort by access count and get top 10
            access_counts.sort(key=lambda x: x[1], reverse=True)
            analytics["most_accessed_contexts"] = access_counts[:10]
            
            return analytics
            
        except Exception as e:
            self.log_error(f"Failed to get context analytics: {e}")
            return {"error": str(e)}
    
    async def _get_context_entry(
        self,
        key: str,
        scope: ContextScope,
        scope_id: str
    ) -> Optional[ContextEntry]:
        """Get context entry from specified scope"""
        try:
            if scope == ContextScope.GLOBAL:
                return self.global_context.get(key)
            elif scope == ContextScope.WORKFLOW:
                return self.workflow_contexts.get(scope_id, {}).get(key)
            elif scope == ContextScope.TASK:
                return self.task_contexts.get(scope_id, {}).get(key)
            elif scope == ContextScope.SESSION:
                return self.session_contexts.get(scope_id, {}).get(key)
            elif scope == ContextScope.AGENT:
                # Agent context is stored differently
                if scope_id in self.agent_contexts:
                    agent_context = self.agent_contexts[scope_id]
                    if key in agent_context.execution_state:
                        return ContextEntry(
                            key=key,
                            value=agent_context.execution_state[key],
                            scope=scope,
                            created_at=agent_context.last_activity,
                            updated_at=agent_context.last_activity
                        )
            
            return None
            
        except Exception as e:
            self.log_error(f"Failed to get context entry: {e}")
            return None
    
    async def _get_inherited_context(
        self,
        key: str,
        scope: ContextScope,
        scope_id: str
    ) -> Any:
        """Get context value through inheritance hierarchy"""
        try:
            # Define inheritance hierarchy
            if scope == ContextScope.TASK:
                # Task -> Agent -> Workflow -> Global
                if scope_id in self.agent_contexts:
                    agent_id = self.agent_contexts[scope_id].agent_id
                    agent_value = await self.get_context(key, ContextScope.AGENT, agent_id, inherit=False)
                    if agent_value is not None:
                        return agent_value
                
                # Try workflow context
                workflow_id = None
                for agent_context in self.agent_contexts.values():
                    if agent_context.current_task_id == scope_id:
                        workflow_id = agent_context.workflow_id
                        break
                
                if workflow_id:
                    workflow_value = await self.get_context(key, ContextScope.WORKFLOW, workflow_id, inherit=False)
                    if workflow_value is not None:
                        return workflow_value
            
            elif scope == ContextScope.AGENT:
                # Agent -> Workflow -> Global
                if scope_id in self.agent_contexts:
                    workflow_id = self.agent_contexts[scope_id].workflow_id
                    workflow_value = await self.get_context(key, ContextScope.WORKFLOW, workflow_id, inherit=False)
                    if workflow_value is not None:
                        return workflow_value
            
            elif scope == ContextScope.WORKFLOW:
                # Workflow -> Global
                pass  # Will check global below
            
            # Always check global as last resort
            global_value = await self.get_context(key, ContextScope.GLOBAL, "global", inherit=False)
            return global_value
            
        except Exception as e:
            self.log_error(f"Failed to get inherited context: {e}")
            return None
    
    async def _store_context_version(self, context_key: str, context_entry: ContextEntry):
        """Store context version for history tracking"""
        try:
            if context_key not in self.context_versions:
                self.context_versions[context_key] = []
            
            version_data = {
                "timestamp": context_entry.updated_at.isoformat(),
                "value": context_entry.value,
                "metadata": context_entry.metadata
            }
            
            self.context_versions[context_key].append(version_data)
            
            # Limit version history
            if len(self.context_versions[context_key]) > 50:
                self.context_versions[context_key] = self.context_versions[context_key][-25:]
                
        except Exception as e:
            self.log_warning(f"Failed to store context version: {e}")
    
    async def _record_access_pattern(self, context_key: str, scope_id: str):
        """Record context access pattern for analytics"""
        try:
            if context_key not in self.access_patterns:
                self.access_patterns[context_key] = []
            
            access_record = {
                "timestamp": datetime.now().isoformat(),
                "scope_id": scope_id
            }
            
            self.access_patterns[context_key].append(access_record)
            
            # Limit access pattern history
            if len(self.access_patterns[context_key]) > 100:
                self.access_patterns[context_key] = self.access_patterns[context_key][-50:]
                
        except Exception as e:
            self.log_warning(f"Failed to record access pattern: {e}")
    
    async def _update_context_metrics(self, operation: str, scope: ContextScope, key: str):
        """Update context usage metrics"""
        try:
            metric_key = f"{operation}_{scope.value}"
            
            if metric_key not in self.context_metrics:
                self.context_metrics[metric_key] = 0
            
            self.context_metrics[metric_key] += 1
            
        except Exception as e:
            self.log_warning(f"Failed to update context metrics: {e}")
    
    async def _periodic_cleanup(self):
        """Periodic cleanup task"""
        try:
            while True:
                await asyncio.sleep(self.cleanup_interval)
                await self.cleanup_expired_context()
                
        except asyncio.CancelledError:
            self.log_info("Context cleanup task cancelled")
        except Exception as e:
            self.log_error(f"Context cleanup task error: {e}")
    
    async def cleanup(self):
        """Cleanup context manager resources"""
        try:
            # Cancel cleanup task
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass
            
            # Clear all context data
            self.global_context.clear()
            self.workflow_contexts.clear()
            self.agent_contexts.clear()
            self.task_contexts.clear()
            self.session_contexts.clear()
            self.context_dependencies.clear()
            self.context_subscribers.clear()
            self.context_versions.clear()
            self.context_locks.clear()
            self.access_patterns.clear()
            self.context_metrics.clear()
            
            self.log_info("Task context manager cleanup completed")
            
        except Exception as e:
            self.log_error(f"Task context manager cleanup failed: {e}")


# Global task context manager instance
task_context_manager = TaskContextManager()
