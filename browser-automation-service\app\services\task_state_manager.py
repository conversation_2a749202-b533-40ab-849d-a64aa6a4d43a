"""
Task State Manager
Robust task tracking system for browser automation workflows
"""

import asyncio
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Callable, Set
from enum import Enum
import json
from dataclasses import dataclass, asdict

from app.models.browser_automation import To<PERSON><PERSON><PERSON>, <PERSON>Status, BrowserAutomationState
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = 10
    HIGH = 8
    MEDIUM = 5
    LOW = 3
    BACKGROUND = 1


class TaskType(Enum):
    """Task type classifications"""
    NAVIGATION = "navigation"
    INTERACTION = "interaction"
    EXTRACTION = "extraction"
    VERIFICATION = "verification"
    ANALYSIS = "analysis"
    COORDINATION = "coordination"


@dataclass
class TaskContext:
    """Context information for task execution"""
    session_id: str
    user_id: str
    workflow_id: str
    agent_role: str
    parent_task_id: Optional[str] = None
    execution_environment: Dict[str, Any] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    
    def __post_init__(self):
        if self.execution_environment is None:
            self.execution_environment = {}


@dataclass
class TaskMetrics:
    """Performance metrics for task execution"""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_duration: Optional[float] = None
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None
    api_calls_made: int = 0
    tokens_consumed: int = 0
    errors_encountered: int = 0
    retries_attempted: int = 0


class TaskStateManager(LoggerMixin):
    """
    Comprehensive task state management system
    
    Features:
    - Real-time task tracking and updates
    - Dependency resolution and management
    - Context preservation across agents
    - Performance metrics collection
    - State persistence and recovery
    - Event-driven notifications
    - Concurrent task coordination
    - Progress analytics and reporting
    """
    
    def __init__(self):
        # Core state storage
        self.tasks: Dict[str, TodoItem] = {}
        self.task_contexts: Dict[str, TaskContext] = {}
        self.task_metrics: Dict[str, TaskMetrics] = {}
        self.task_dependencies: Dict[str, Set[str]] = {}
        self.task_dependents: Dict[str, Set[str]] = {}
        
        # Workflow management
        self.workflows: Dict[str, BrowserAutomationState] = {}
        self.workflow_tasks: Dict[str, Set[str]] = {}
        
        # Event system
        self.event_listeners: Dict[str, List[Callable]] = {}
        self.task_history: List[Dict[str, Any]] = []
        
        # Performance tracking
        self.execution_stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_execution_time": 0.0,
            "success_rate": 0.0
        }
        
        # Concurrency control
        self.task_locks: Dict[str, asyncio.Lock] = {}
        self.state_lock = asyncio.Lock()
        
        self.log_info("Task state manager initialized")
    
    async def create_task(
        self,
        task_description: str,
        context: TaskContext,
        priority: TaskPriority = TaskPriority.MEDIUM,
        task_type: TaskType = TaskType.INTERACTION,
        dependencies: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        Create a new task with full context and tracking
        
        Args:
            task_description: Description of the task to be performed
            context: Task execution context
            priority: Task priority level
            task_type: Type of task for categorization
            dependencies: List of task IDs this task depends on
            metadata: Additional metadata for the task
            
        Returns:
            Task ID for the created task
        """
        try:
            async with self.state_lock:
                task_id = str(uuid.uuid4())
                
                # Create TodoItem
                todo_item = TodoItem(
                    id=task_id,
                    task=task_description,
                    status=TaskStatus.PENDING,
                    priority=priority.value,
                    assigned_agent=context.agent_role,
                    dependencies=dependencies or [],
                    result=None
                )
                
                # Store task and context
                self.tasks[task_id] = todo_item
                self.task_contexts[task_id] = context
                self.task_metrics[task_id] = TaskMetrics()
                
                # Set up dependencies
                if dependencies:
                    self.task_dependencies[task_id] = set(dependencies)
                    for dep_id in dependencies:
                        if dep_id not in self.task_dependents:
                            self.task_dependents[dep_id] = set()
                        self.task_dependents[dep_id].add(task_id)
                else:
                    self.task_dependencies[task_id] = set()
                
                # Add to workflow
                if context.workflow_id not in self.workflow_tasks:
                    self.workflow_tasks[context.workflow_id] = set()
                self.workflow_tasks[context.workflow_id].add(task_id)
                
                # Create task lock
                self.task_locks[task_id] = asyncio.Lock()
                
                # Update statistics
                self.execution_stats["total_tasks"] += 1
                
                # Emit event
                await self._emit_event("task_created", {
                    "task_id": task_id,
                    "description": task_description,
                    "context": asdict(context),
                    "priority": priority.value,
                    "type": task_type.value
                })
                
                self.log_info(f"Task created: {task_id}", description=task_description[:50])
                
                return task_id
                
        except Exception as e:
            self.log_error(f"Failed to create task: {e}")
            raise BrowserAutomationException(f"Task creation failed: {e}")
    
    async def update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        result: Any = None,
        error_message: str = None,
        progress_data: Dict[str, Any] = None
    ) -> bool:
        """
        Update task status with comprehensive tracking
        
        Args:
            task_id: ID of the task to update
            status: New status for the task
            result: Task execution result (if completed)
            error_message: Error message (if failed)
            progress_data: Additional progress information
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            if task_id not in self.tasks:
                self.log_warning(f"Task not found for update: {task_id}")
                return False
            
            async with self.task_locks[task_id]:
                task = self.tasks[task_id]
                old_status = task.status
                
                # Update task
                task.status = status
                if result is not None:
                    task.result = result
                if error_message:
                    task.error_message = error_message
                
                # Update metrics
                metrics = self.task_metrics[task_id]
                
                if status == TaskStatus.IN_PROGRESS and old_status == TaskStatus.PENDING:
                    metrics.start_time = datetime.now()
                elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    if metrics.start_time:
                        metrics.end_time = datetime.now()
                        metrics.execution_duration = (metrics.end_time - metrics.start_time).total_seconds()
                    
                    task.completion_time = datetime.now()
                    
                    # Update global statistics
                    if status == TaskStatus.COMPLETED:
                        self.execution_stats["completed_tasks"] += 1
                    elif status == TaskStatus.FAILED:
                        self.execution_stats["failed_tasks"] += 1
                        metrics.errors_encountered += 1
                
                # Record state change in history
                await self._record_state_change(task_id, old_status, status, progress_data)
                
                # Check and update dependent tasks
                if status == TaskStatus.COMPLETED:
                    await self._check_dependent_tasks(task_id)
                
                # Emit event
                await self._emit_event("task_status_updated", {
                    "task_id": task_id,
                    "old_status": old_status.value,
                    "new_status": status.value,
                    "result": result,
                    "error_message": error_message,
                    "progress_data": progress_data
                })
                
                self.log_info(f"Task status updated: {task_id}", 
                             old_status=old_status.value, new_status=status.value)
                
                return True
                
        except Exception as e:
            self.log_error(f"Failed to update task status: {e}")
            return False
    
    async def get_ready_tasks(
        self,
        workflow_id: str = None,
        agent_role: str = None,
        task_type: TaskType = None,
        max_tasks: int = 10
    ) -> List[TodoItem]:
        """
        Get tasks that are ready for execution (dependencies satisfied)
        
        Args:
            workflow_id: Filter by workflow ID
            agent_role: Filter by assigned agent role
            task_type: Filter by task type
            max_tasks: Maximum number of tasks to return
            
        Returns:
            List of tasks ready for execution
        """
        try:
            ready_tasks = []
            
            for task_id, task in self.tasks.items():
                # Skip if not pending
                if task.status != TaskStatus.PENDING:
                    continue
                
                # Apply filters
                if workflow_id and self.task_contexts[task_id].workflow_id != workflow_id:
                    continue
                
                if agent_role and task.assigned_agent != agent_role:
                    continue
                
                # Check dependencies
                dependencies = self.task_dependencies.get(task_id, set())
                if dependencies:
                    # Check if all dependencies are completed
                    all_deps_completed = all(
                        self.tasks[dep_id].status == TaskStatus.COMPLETED
                        for dep_id in dependencies
                        if dep_id in self.tasks
                    )
                    if not all_deps_completed:
                        continue
                
                ready_tasks.append(task)
                
                if len(ready_tasks) >= max_tasks:
                    break
            
            # Sort by priority (higher priority first)
            ready_tasks.sort(key=lambda t: t.priority, reverse=True)
            
            return ready_tasks
            
        except Exception as e:
            self.log_error(f"Failed to get ready tasks: {e}")
            return []
    
    async def get_workflow_progress(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get comprehensive progress information for a workflow
        
        Args:
            workflow_id: ID of the workflow to analyze
            
        Returns:
            Dictionary containing progress metrics and analysis
        """
        try:
            if workflow_id not in self.workflow_tasks:
                return {"error": "Workflow not found"}
            
            task_ids = self.workflow_tasks[workflow_id]
            tasks = [self.tasks[tid] for tid in task_ids if tid in self.tasks]
            
            # Calculate status distribution
            status_counts = {}
            for status in TaskStatus:
                status_counts[status.value] = sum(1 for t in tasks if t.status == status)
            
            total_tasks = len(tasks)
            completed_tasks = status_counts.get(TaskStatus.COMPLETED.value, 0)
            failed_tasks = status_counts.get(TaskStatus.FAILED.value, 0)
            in_progress_tasks = status_counts.get(TaskStatus.IN_PROGRESS.value, 0)
            pending_tasks = status_counts.get(TaskStatus.PENDING.value, 0)
            
            # Calculate progress percentage
            progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            # Calculate estimated completion time
            estimated_completion = await self._estimate_completion_time(workflow_id)
            
            # Get performance metrics
            performance_metrics = await self._calculate_workflow_performance(workflow_id)
            
            return {
                "workflow_id": workflow_id,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "failed_tasks": failed_tasks,
                "in_progress_tasks": in_progress_tasks,
                "pending_tasks": pending_tasks,
                "progress_percentage": progress_percentage,
                "status_distribution": status_counts,
                "estimated_completion": estimated_completion,
                "performance_metrics": performance_metrics,
                "success_rate": (completed_tasks / (completed_tasks + failed_tasks)) * 100 if (completed_tasks + failed_tasks) > 0 else 0
            }
            
        except Exception as e:
            self.log_error(f"Failed to get workflow progress: {e}")
            return {"error": str(e)}
    
    async def retry_failed_task(
        self,
        task_id: str,
        reset_dependencies: bool = False
    ) -> bool:
        """
        Retry a failed task with updated context
        
        Args:
            task_id: ID of the task to retry
            reset_dependencies: Whether to reset dependency status
            
        Returns:
            True if retry was initiated, False otherwise
        """
        try:
            if task_id not in self.tasks:
                return False
            
            task = self.tasks[task_id]
            context = self.task_contexts[task_id]
            metrics = self.task_metrics[task_id]
            
            # Check retry limits
            if context.retry_count >= context.max_retries:
                self.log_warning(f"Task {task_id} exceeded max retries")
                return False
            
            # Update retry count
            context.retry_count += 1
            metrics.retries_attempted += 1
            
            # Reset task status
            task.status = TaskStatus.PENDING
            task.error_message = None
            task.completion_time = None
            
            # Reset metrics
            metrics.start_time = None
            metrics.end_time = None
            metrics.execution_duration = None
            
            # Reset dependencies if requested
            if reset_dependencies:
                for dep_id in self.task_dependencies.get(task_id, set()):
                    if dep_id in self.tasks and self.tasks[dep_id].status == TaskStatus.FAILED:
                        await self.retry_failed_task(dep_id, reset_dependencies=True)
            
            # Emit event
            await self._emit_event("task_retry_initiated", {
                "task_id": task_id,
                "retry_count": context.retry_count,
                "max_retries": context.max_retries
            })
            
            self.log_info(f"Task retry initiated: {task_id}", retry_count=context.retry_count)
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to retry task: {e}")
            return False
    
    async def add_task_dependency(
        self,
        task_id: str,
        dependency_id: str
    ) -> bool:
        """
        Add a dependency relationship between tasks
        
        Args:
            task_id: ID of the task that depends on another
            dependency_id: ID of the task that must complete first
            
        Returns:
            True if dependency was added, False otherwise
        """
        try:
            if task_id not in self.tasks or dependency_id not in self.tasks:
                return False
            
            # Check for circular dependencies
            if await self._would_create_cycle(task_id, dependency_id):
                self.log_warning(f"Circular dependency detected: {task_id} -> {dependency_id}")
                return False
            
            # Add dependency
            if task_id not in self.task_dependencies:
                self.task_dependencies[task_id] = set()
            self.task_dependencies[task_id].add(dependency_id)
            
            # Add to dependents
            if dependency_id not in self.task_dependents:
                self.task_dependents[dependency_id] = set()
            self.task_dependents[dependency_id].add(task_id)
            
            # Update task dependencies list
            task = self.tasks[task_id]
            if dependency_id not in task.dependencies:
                task.dependencies.append(dependency_id)
            
            self.log_info(f"Dependency added: {task_id} depends on {dependency_id}")
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to add task dependency: {e}")
            return False
    
    async def get_task_chain(self, task_id: str) -> Dict[str, Any]:
        """
        Get the complete dependency chain for a task
        
        Args:
            task_id: ID of the task to analyze
            
        Returns:
            Dictionary containing dependency chain information
        """
        try:
            if task_id not in self.tasks:
                return {"error": "Task not found"}
            
            # Get all dependencies (recursive)
            all_dependencies = await self._get_all_dependencies(task_id)
            
            # Get all dependents (recursive)
            all_dependents = await self._get_all_dependents(task_id)
            
            # Calculate critical path
            critical_path = await self._calculate_critical_path(task_id)
            
            return {
                "task_id": task_id,
                "direct_dependencies": list(self.task_dependencies.get(task_id, set())),
                "all_dependencies": list(all_dependencies),
                "direct_dependents": list(self.task_dependents.get(task_id, set())),
                "all_dependents": list(all_dependents),
                "critical_path": critical_path,
                "dependency_depth": len(all_dependencies),
                "dependent_count": len(all_dependents)
            }
            
        except Exception as e:
            self.log_error(f"Failed to get task chain: {e}")
            return {"error": str(e)}
    
    async def register_event_listener(
        self,
        event_type: str,
        callback: Callable[[Dict[str, Any]], None]
    ):
        """
        Register an event listener for task state changes
        
        Args:
            event_type: Type of event to listen for
            callback: Function to call when event occurs
        """
        if event_type not in self.event_listeners:
            self.event_listeners[event_type] = []
        
        self.event_listeners[event_type].append(callback)
        self.log_info(f"Event listener registered for: {event_type}")
    
    async def get_execution_statistics(self) -> Dict[str, Any]:
        """Get comprehensive execution statistics"""
        try:
            # Update success rate
            total_completed = self.execution_stats["completed_tasks"] + self.execution_stats["failed_tasks"]
            if total_completed > 0:
                self.execution_stats["success_rate"] = (self.execution_stats["completed_tasks"] / total_completed) * 100
            
            # Calculate average execution time
            completed_tasks = [
                task_id for task_id, task in self.tasks.items()
                if task.status == TaskStatus.COMPLETED
            ]
            
            if completed_tasks:
                total_time = sum(
                    self.task_metrics[task_id].execution_duration or 0
                    for task_id in completed_tasks
                )
                self.execution_stats["average_execution_time"] = total_time / len(completed_tasks)
            
            # Add additional metrics
            current_stats = self.execution_stats.copy()
            current_stats.update({
                "active_workflows": len(self.workflows),
                "pending_tasks": sum(1 for t in self.tasks.values() if t.status == TaskStatus.PENDING),
                "in_progress_tasks": sum(1 for t in self.tasks.values() if t.status == TaskStatus.IN_PROGRESS),
                "total_dependencies": sum(len(deps) for deps in self.task_dependencies.values()),
                "event_listeners": sum(len(listeners) for listeners in self.event_listeners.values())
            })
            
            return current_stats
            
        except Exception as e:
            self.log_error(f"Failed to get execution statistics: {e}")
            return {"error": str(e)}
    
    # Helper methods for internal operations
    async def _emit_event(self, event_type: str, event_data: Dict[str, Any]):
        """Emit an event to all registered listeners"""
        try:
            if event_type in self.event_listeners:
                for callback in self.event_listeners[event_type]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(event_data)
                        else:
                            callback(event_data)
                    except Exception as e:
                        self.log_warning(f"Event listener failed: {e}")
        except Exception as e:
            self.log_error(f"Failed to emit event: {e}")
    
    async def _record_state_change(
        self,
        task_id: str,
        old_status: TaskStatus,
        new_status: TaskStatus,
        progress_data: Dict[str, Any] = None
    ):
        """Record task state change in history"""
        try:
            history_entry = {
                "task_id": task_id,
                "timestamp": datetime.now().isoformat(),
                "old_status": old_status.value,
                "new_status": new_status.value,
                "progress_data": progress_data or {}
            }
            
            self.task_history.append(history_entry)
            
            # Limit history size
            if len(self.task_history) > 1000:
                self.task_history = self.task_history[-500:]
                
        except Exception as e:
            self.log_warning(f"Failed to record state change: {e}")
    
    async def _check_dependent_tasks(self, completed_task_id: str):
        """Check if any dependent tasks are now ready to execute"""
        try:
            dependents = self.task_dependents.get(completed_task_id, set())
            
            for dependent_id in dependents:
                if dependent_id not in self.tasks:
                    continue
                
                dependent_task = self.tasks[dependent_id]
                if dependent_task.status != TaskStatus.PENDING:
                    continue
                
                # Check if all dependencies are now completed
                dependencies = self.task_dependencies.get(dependent_id, set())
                all_completed = all(
                    self.tasks[dep_id].status == TaskStatus.COMPLETED
                    for dep_id in dependencies
                    if dep_id in self.tasks
                )
                
                if all_completed:
                    await self._emit_event("task_ready", {
                        "task_id": dependent_id,
                        "dependencies_completed": list(dependencies)
                    })
                    
        except Exception as e:
            self.log_warning(f"Failed to check dependent tasks: {e}")
    
    async def _would_create_cycle(self, task_id: str, dependency_id: str) -> bool:
        """Check if adding a dependency would create a circular dependency"""
        try:
            # Use DFS to check for cycles
            visited = set()
            rec_stack = set()
            
            def has_cycle(node: str) -> bool:
                if node in rec_stack:
                    return True
                if node in visited:
                    return False
                
                visited.add(node)
                rec_stack.add(node)
                
                # Check all dependencies
                for dep in self.task_dependencies.get(node, set()):
                    if has_cycle(dep):
                        return True
                
                rec_stack.remove(node)
                return False
            
            # Temporarily add the dependency and check for cycles
            if dependency_id not in self.task_dependencies:
                self.task_dependencies[dependency_id] = set()
            
            original_deps = self.task_dependencies.get(task_id, set()).copy()
            self.task_dependencies[task_id] = original_deps | {dependency_id}
            
            cycle_detected = has_cycle(task_id)
            
            # Restore original dependencies
            self.task_dependencies[task_id] = original_deps
            
            return cycle_detected
            
        except Exception as e:
            self.log_error(f"Failed to check for cycles: {e}")
            return True  # Err on the side of caution
    
    async def _get_all_dependencies(self, task_id: str, visited: Set[str] = None) -> Set[str]:
        """Get all dependencies recursively"""
        if visited is None:
            visited = set()
        
        if task_id in visited:
            return set()
        
        visited.add(task_id)
        all_deps = set()
        
        direct_deps = self.task_dependencies.get(task_id, set())
        all_deps.update(direct_deps)
        
        for dep_id in direct_deps:
            all_deps.update(await self._get_all_dependencies(dep_id, visited))
        
        return all_deps
    
    async def _get_all_dependents(self, task_id: str, visited: Set[str] = None) -> Set[str]:
        """Get all dependents recursively"""
        if visited is None:
            visited = set()
        
        if task_id in visited:
            return set()
        
        visited.add(task_id)
        all_dependents = set()
        
        direct_dependents = self.task_dependents.get(task_id, set())
        all_dependents.update(direct_dependents)
        
        for dependent_id in direct_dependents:
            all_dependents.update(await self._get_all_dependents(dependent_id, visited))
        
        return all_dependents
    
    async def _calculate_critical_path(self, task_id: str) -> List[str]:
        """Calculate the critical path for task completion"""
        # Simplified critical path calculation
        # In a full implementation, this would use proper CPM algorithms
        try:
            all_deps = await self._get_all_dependencies(task_id)
            
            # For now, return the longest dependency chain
            longest_chain = [task_id]
            current_task = task_id
            
            while True:
                deps = self.task_dependencies.get(current_task, set())
                if not deps:
                    break
                
                # Find the dependency with the longest chain
                longest_dep = None
                max_chain_length = 0
                
                for dep_id in deps:
                    dep_chain = await self._get_all_dependencies(dep_id)
                    if len(dep_chain) > max_chain_length:
                        max_chain_length = len(dep_chain)
                        longest_dep = dep_id
                
                if longest_dep:
                    longest_chain.insert(0, longest_dep)
                    current_task = longest_dep
                else:
                    break
            
            return longest_chain
            
        except Exception as e:
            self.log_error(f"Failed to calculate critical path: {e}")
            return [task_id]
    
    async def _estimate_completion_time(self, workflow_id: str) -> Optional[str]:
        """Estimate workflow completion time based on current progress"""
        try:
            task_ids = self.workflow_tasks.get(workflow_id, set())
            if not task_ids:
                return None
            
            # Calculate average task execution time
            completed_tasks = [
                tid for tid in task_ids
                if tid in self.tasks and self.tasks[tid].status == TaskStatus.COMPLETED
            ]
            
            if not completed_tasks:
                return "Unable to estimate"
            
            total_time = sum(
                self.task_metrics[tid].execution_duration or 0
                for tid in completed_tasks
            )
            avg_time = total_time / len(completed_tasks)
            
            # Count remaining tasks
            remaining_tasks = sum(
                1 for tid in task_ids
                if tid in self.tasks and self.tasks[tid].status in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]
            )
            
            estimated_seconds = remaining_tasks * avg_time
            estimated_completion = datetime.now() + timedelta(seconds=estimated_seconds)
            
            return estimated_completion.isoformat()
            
        except Exception as e:
            self.log_error(f"Failed to estimate completion time: {e}")
            return None
    
    async def _calculate_workflow_performance(self, workflow_id: str) -> Dict[str, Any]:
        """Calculate performance metrics for a workflow"""
        try:
            task_ids = self.workflow_tasks.get(workflow_id, set())
            
            metrics = {
                "total_execution_time": 0.0,
                "average_task_time": 0.0,
                "total_api_calls": 0,
                "total_tokens": 0,
                "total_errors": 0,
                "total_retries": 0
            }
            
            completed_tasks = []
            for tid in task_ids:
                if tid in self.task_metrics:
                    task_metrics = self.task_metrics[tid]
                    if task_metrics.execution_duration:
                        completed_tasks.append(task_metrics)
                        metrics["total_execution_time"] += task_metrics.execution_duration
                        metrics["total_api_calls"] += task_metrics.api_calls_made
                        metrics["total_tokens"] += task_metrics.tokens_consumed
                        metrics["total_errors"] += task_metrics.errors_encountered
                        metrics["total_retries"] += task_metrics.retries_attempted
            
            if completed_tasks:
                metrics["average_task_time"] = metrics["total_execution_time"] / len(completed_tasks)
            
            return metrics
            
        except Exception as e:
            self.log_error(f"Failed to calculate workflow performance: {e}")
            return {}
    
    async def cleanup(self):
        """Cleanup task state manager resources"""
        try:
            # Clear all data structures
            self.tasks.clear()
            self.task_contexts.clear()
            self.task_metrics.clear()
            self.task_dependencies.clear()
            self.task_dependents.clear()
            self.workflows.clear()
            self.workflow_tasks.clear()
            self.event_listeners.clear()
            self.task_history.clear()
            self.task_locks.clear()
            
            self.log_info("Task state manager cleanup completed")
            
        except Exception as e:
            self.log_error(f"Task state manager cleanup failed: {e}")


# Global task state manager instance
task_state_manager = TaskStateManager()
