"""
Quick Validation Script
Fast validation of browser automation system components and basic functionality
"""

import asyncio
from datetime import datetime
from typing import Dict, Any
import sys
import traceback

# Import core components
from app.services.session_manager import session_manager, SessionPriority, SessionConfig
from app.services.performance_optimizer import performance_optimizer, PerformanceMetric
from app.services.access_control import access_control_manager, FeatureType, SubscriptionTier
from app.services.langgraph_workflow import langgraph_workflow_manager
from app.api.test_endpoint import test_endpoint_api


class QuickValidator:
    """Quick validation of system components"""
    
    def __init__(self):
        self.validation_results = {}
        self.start_time = None
        self.errors = []
        self.warnings = []
    
    async def run_quick_validation(self) -> Dict[str, Any]:
        """Run quick validation of all components"""
        
        print("⚡ Quick Validation - Browser Automation System")
        print("=" * 50)
        
        self.start_time = datetime.now()
        
        try:
            # Test 1: Service Initialization
            await self._test_service_initialization()
            
            # Test 2: Session Management
            await self._test_session_management()
            
            # Test 3: Performance Optimization
            await self._test_performance_optimization()
            
            # Test 4: Access Control
            await self._test_access_control()
            
            # Test 5: LangGraph Workflow
            await self._test_langgraph_workflow()
            
            # Test 6: API Endpoints
            await self._test_api_endpoints()
            
            # Generate summary
            return await self._generate_validation_summary()
            
        except Exception as e:
            self.errors.append(f"Validation failed: {e}")
            print(f"❌ Validation failed: {e}")
            return {"success": False, "error": str(e)}
        
        finally:
            # Cleanup
            await self._cleanup()
    
    async def _test_service_initialization(self):
        """Test service initialization"""
        print("\n1️⃣ Testing Service Initialization...")
        
        try:
            # Initialize session manager
            await session_manager.initialize()
            print("   ✅ Session Manager initialized")
            
            # Initialize performance optimizer
            await performance_optimizer.initialize()
            print("   ✅ Performance Optimizer initialized")
            
            # Initialize access control manager
            await access_control_manager.initialize()
            print("   ✅ Access Control Manager initialized")
            
            self.validation_results["service_initialization"] = {
                "success": True,
                "services_initialized": ["session_manager", "performance_optimizer", "access_control"]
            }
            
        except Exception as e:
            self.errors.append(f"Service initialization failed: {e}")
            print(f"   ❌ Service initialization failed: {e}")
            self.validation_results["service_initialization"] = {
                "success": False,
                "error": str(e)
            }
    
    async def _test_session_management(self):
        """Test session management functionality"""
        print("\n2️⃣ Testing Session Management...")
        
        try:
            # Test session creation
            session = await session_manager.get_session(
                user_id="test_user",
                priority=SessionPriority.NORMAL,
                config=SessionConfig(headless=True, viewport_width=1920)
            )
            print("   ✅ Session created successfully")
            
            # Test session metrics
            metrics = await session_manager.get_session_metrics(session.session_id)
            print("   ✅ Session metrics retrieved")
            
            # Test session release
            await session_manager.release_session(session.session_id)
            print("   ✅ Session released successfully")
            
            # Test session pool optimization
            optimization_result = await session_manager.optimize_session_pool()
            print("   ✅ Session pool optimization completed")
            
            self.validation_results["session_management"] = {
                "success": True,
                "session_created": True,
                "metrics_available": "error" not in metrics,
                "optimization_successful": "error" not in optimization_result
            }
            
        except Exception as e:
            self.errors.append(f"Session management test failed: {e}")
            print(f"   ❌ Session management test failed: {e}")
            self.validation_results["session_management"] = {
                "success": False,
                "error": str(e)
            }
    
    async def _test_performance_optimization(self):
        """Test performance optimization functionality"""
        print("\n3️⃣ Testing Performance Optimization...")
        
        try:
            # Test metric recording
            await performance_optimizer.record_metric(
                PerformanceMetric.RESPONSE_TIME, 2.5
            )
            print("   ✅ Performance metric recorded")
            
            # Test performance report generation
            report = await performance_optimizer.get_performance_report("1h")
            print("   ✅ Performance report generated")
            
            # Test optimization execution
            from app.services.performance_optimizer import OptimizationStrategy
            optimization_result = await performance_optimizer.optimize_performance(
                OptimizationStrategy.MEMORY_OPTIMIZATION
            )
            print("   ✅ Performance optimization executed")
            
            # Test performance prediction
            predictions = await performance_optimizer.predict_performance_issues()
            print("   ✅ Performance prediction completed")
            
            self.validation_results["performance_optimization"] = {
                "success": True,
                "metric_recording": True,
                "report_generation": "error" not in report,
                "optimization_execution": optimization_result.success,
                "prediction_available": isinstance(predictions, list)
            }
            
        except Exception as e:
            self.errors.append(f"Performance optimization test failed: {e}")
            print(f"   ❌ Performance optimization test failed: {e}")
            self.validation_results["performance_optimization"] = {
                "success": False,
                "error": str(e)
            }
    
    async def _test_access_control(self):
        """Test access control functionality"""
        print("\n4️⃣ Testing Access Control...")
        
        try:
            # Test tier-based access
            access_result = await access_control_manager.check_access(
                user_id="test_user",
                feature=FeatureType.BASIC_BROWSING
            )
            print("   ✅ Access control check completed")
            
            # Test quota management
            quota_status = await access_control_manager.check_browsing_quota("test_user")
            print("   ✅ Quota status retrieved")
            
            # Test user limits
            user_limits = await access_control_manager.get_user_limits("test_user")
            print("   ✅ User limits retrieved")
            
            # Test usage tracking
            await access_control_manager.track_usage("test_user", FeatureType.BASIC_BROWSING)
            print("   ✅ Usage tracking completed")
            
            self.validation_results["access_control"] = {
                "success": True,
                "access_check": "error" not in access_result,
                "quota_management": "error" not in quota_status,
                "user_limits": "error" not in user_limits,
                "usage_tracking": True
            }
            
        except Exception as e:
            self.errors.append(f"Access control test failed: {e}")
            print(f"   ❌ Access control test failed: {e}")
            self.validation_results["access_control"] = {
                "success": False,
                "error": str(e)
            }
    
    async def _test_langgraph_workflow(self):
        """Test LangGraph workflow functionality"""
        print("\n5️⃣ Testing LangGraph Workflow...")
        
        try:
            # Test workflow manager readiness
            is_ready = langgraph_workflow_manager.is_ready()
            print(f"   ✅ Workflow manager ready: {is_ready}")
            
            # Test available workflows
            available_workflows = len(langgraph_workflow_manager.available_workflows)
            print(f"   ✅ Available workflows: {available_workflows}")
            
            # Test workflow configuration
            workflow_config = langgraph_workflow_manager.get_workflow_config("hierarchical")
            print("   ✅ Workflow configuration retrieved")
            
            self.validation_results["langgraph_workflow"] = {
                "success": True,
                "workflow_manager_ready": is_ready,
                "available_workflows": available_workflows,
                "configuration_available": workflow_config is not None
            }
            
        except Exception as e:
            self.errors.append(f"LangGraph workflow test failed: {e}")
            print(f"   ❌ LangGraph workflow test failed: {e}")
            self.validation_results["langgraph_workflow"] = {
                "success": False,
                "error": str(e)
            }
    
    async def _test_api_endpoints(self):
        """Test API endpoints functionality"""
        print("\n6️⃣ Testing API Endpoints...")
        
        try:
            # Test endpoint initialization
            endpoint_ready = test_endpoint_api is not None
            print(f"   ✅ Test endpoint API ready: {endpoint_ready}")
            
            # Test router availability
            router_available = hasattr(test_endpoint_api, 'router')
            print(f"   ✅ Router available: {router_available}")
            
            # Test route count
            if router_available:
                route_count = len(test_endpoint_api.router.routes)
                print(f"   ✅ API routes available: {route_count}")
            else:
                route_count = 0
            
            self.validation_results["api_endpoints"] = {
                "success": endpoint_ready and router_available,
                "endpoint_ready": endpoint_ready,
                "router_available": router_available,
                "route_count": route_count
            }
            
        except Exception as e:
            self.errors.append(f"API endpoints test failed: {e}")
            print(f"   ❌ API endpoints test failed: {e}")
            self.validation_results["api_endpoints"] = {
                "success": False,
                "error": str(e)
            }
    
    async def _generate_validation_summary(self) -> Dict[str, Any]:
        """Generate validation summary"""
        print("\n📊 Validation Summary")
        print("-" * 30)
        
        # Calculate statistics
        total_tests = len(self.validation_results)
        successful_tests = sum(1 for result in self.validation_results.values() 
                             if result.get("success", False))
        failed_tests = total_tests - successful_tests
        
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        overall_status = "PASSED" if success_rate >= 80 and len(self.errors) == 0 else "FAILED"
        
        execution_time = (datetime.now() - self.start_time).total_seconds()
        
        # Print summary
        print(f"Overall Status: {overall_status}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Tests Passed: {successful_tests}/{total_tests}")
        print(f"Execution Time: {execution_time:.2f} seconds")
        
        if self.errors:
            print(f"\n❌ Errors ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️ Warnings ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        # Create summary report
        summary = {
            "validation_info": {
                "timestamp": datetime.now().isoformat(),
                "execution_time": execution_time,
                "validator_version": "1.0.0"
            },
            "overall_results": {
                "status": overall_status,
                "success_rate": success_rate,
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests
            },
            "detailed_results": self.validation_results,
            "errors": self.errors,
            "warnings": self.warnings,
            "recommendations": self._generate_recommendations()
        }
        
        return summary
    
    def _generate_recommendations(self) -> list:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        if self.errors:
            recommendations.append("Address all error conditions before deployment")
        
        if self.warnings:
            recommendations.append("Review warning conditions for optimal performance")
        
        # Check specific components
        if not self.validation_results.get("session_management", {}).get("success", False):
            recommendations.append("Fix session management issues")
        
        if not self.validation_results.get("performance_optimization", {}).get("success", False):
            recommendations.append("Resolve performance optimization problems")
        
        if not self.validation_results.get("access_control", {}).get("success", False):
            recommendations.append("Fix access control configuration")
        
        if not recommendations:
            recommendations.append("System validation passed - ready for deployment")
        
        return recommendations
    
    async def _cleanup(self):
        """Cleanup resources"""
        try:
            await session_manager.shutdown()
            await performance_optimizer.shutdown()
            print("\n🧹 Cleanup completed")
        except Exception as e:
            print(f"\n⚠️ Cleanup warning: {e}")


async def run_quick_validation():
    """Run quick validation"""
    
    print("⚡ Browser Automation System - Quick Validation")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        validator = QuickValidator()
        summary = await validator.run_quick_validation()
        
        # Print final status
        if summary.get("overall_results", {}).get("status") == "PASSED":
            print("\n🎉 QUICK VALIDATION PASSED!")
            print("✅ Browser automation system components are operational")
        else:
            print("\n⚠️ QUICK VALIDATION FAILED!")
            print("❌ Review errors and fix issues")
        
        return summary
        
    except Exception as e:
        print(f"\n💥 Quick validation failed: {e}")
        traceback.print_exc()
        return {"success": False, "error": str(e)}


if __name__ == "__main__":
    print("🚀 Starting Quick Validation...")
    
    # Run quick validation
    result = asyncio.run(run_quick_validation())
    
    # Exit with appropriate code
    if result.get("overall_results", {}).get("status") == "PASSED":
        sys.exit(0)
    else:
        sys.exit(1)
