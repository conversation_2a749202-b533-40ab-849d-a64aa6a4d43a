"""
Test Runner Script
Demonstrates the test endpoint functionality and validates the browser automation system
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any
import sys

# Import test modules
from quick_validation import run_quick_validation
from test_comprehensive_validation import run_comprehensive_tests


async def run_test_endpoint_demo():
    """Demonstrate test endpoint functionality"""
    
    print("🧪 Test Endpoint Demonstration")
    print("=" * 40)
    
    try:
        # Import test endpoint API
        from app.api.test_endpoint import test_endpoint_api, TestRequest, ValidationRequest
        
        print("\n1️⃣ Testing Browser Automation Endpoint...")
        
        # Test browser automation
        browser_request = TestRequest(
            test_type="basic_navigation",
            user_id="demo_user",
            test_config={"test_url": "https://example.com"},
            subscription_tier="pro",
            timeout_seconds=60
        )
        
        # Execute browser automation test
        await test_endpoint_api._execute_browser_automation_test(
            test_id="demo_browser_test",
            request=browser_request
        )
        
        # Get result
        browser_result = test_endpoint_api.test_results.get("demo_browser_test")
        if browser_result:
            print(f"   ✅ Browser automation test: {browser_result.status}")
            print(f"   📊 Success: {browser_result.success}")
            print(f"   ⏱️ Execution time: {browser_result.execution_time:.2f}s")
        
        print("\n2️⃣ Testing LangGraph Workflow Endpoint...")
        
        # Test LangGraph workflow
        workflow_request = TestRequest(
            test_type="supervisor_agent",
            user_id="demo_user",
            test_config={"workflow_type": "hierarchical"},
            subscription_tier="enterprise"
        )
        
        # Execute workflow test
        await test_endpoint_api._execute_langgraph_workflow_test(
            test_id="demo_workflow_test",
            request=workflow_request
        )
        
        # Get result
        workflow_result = test_endpoint_api.test_results.get("demo_workflow_test")
        if workflow_result:
            print(f"   ✅ LangGraph workflow test: {workflow_result.status}")
            print(f"   📊 Success: {workflow_result.success}")
            print(f"   ⏱️ Execution time: {workflow_result.execution_time:.2f}s")
        
        print("\n3️⃣ Testing System Validation...")
        
        # Test system validation
        validation_request = ValidationRequest(
            validation_scope="quick",
            include_performance=True,
            include_security=True
        )
        
        # Execute validation
        validation_result = await test_endpoint_api._execute_system_validation(validation_request)
        
        print(f"   ✅ System validation: {'PASSED' if validation_result.get('success') else 'FAILED'}")
        print(f"   📊 Components validated: {len(validation_result.get('components', {}))}")
        
        print("\n✅ Test endpoint demonstration completed")
        
        return {
            "browser_automation": browser_result.success if browser_result else False,
            "langgraph_workflow": workflow_result.success if workflow_result else False,
            "system_validation": validation_result.get("success", False)
        }
        
    except Exception as e:
        print(f"❌ Test endpoint demonstration failed: {e}")
        return {"success": False, "error": str(e)}


async def run_all_validation_tests():
    """Run all validation tests"""
    
    print("🚀 Browser Automation System - Complete Validation Suite")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {
        "quick_validation": None,
        "test_endpoints": None,
        "comprehensive_tests": None,
        "overall_success": False
    }
    
    try:
        # 1. Quick Validation
        print("\n🔥 Phase 1: Quick Validation")
        print("-" * 30)
        
        quick_result = await run_quick_validation()
        results["quick_validation"] = quick_result
        
        quick_success = quick_result.get("overall_results", {}).get("status") == "PASSED"
        print(f"Quick Validation: {'✅ PASSED' if quick_success else '❌ FAILED'}")
        
        # 2. Test Endpoint Demonstration
        print("\n🧪 Phase 2: Test Endpoint Demonstration")
        print("-" * 40)
        
        endpoint_result = await run_test_endpoint_demo()
        results["test_endpoints"] = endpoint_result
        
        endpoint_success = not endpoint_result.get("error") and all(endpoint_result.values())
        print(f"Test Endpoints: {'✅ PASSED' if endpoint_success else '❌ FAILED'}")
        
        # 3. Comprehensive Tests (if quick validation passed)
        if quick_success:
            print("\n🎯 Phase 3: Comprehensive Test Suite")
            print("-" * 35)
            
            comprehensive_result = await run_comprehensive_tests()
            results["comprehensive_tests"] = comprehensive_result
            
            comprehensive_success = comprehensive_result.get("overall_results", {}).get("status") == "PASSED"
            print(f"Comprehensive Tests: {'✅ PASSED' if comprehensive_success else '❌ FAILED'}")
        else:
            print("\n⚠️ Phase 3: Skipped (Quick validation failed)")
            comprehensive_success = False
        
        # Overall assessment
        results["overall_success"] = quick_success and endpoint_success and comprehensive_success
        
        # Generate final report
        print(f"\n📊 Final Validation Report")
        print("=" * 30)
        print(f"Quick Validation: {'✅' if quick_success else '❌'}")
        print(f"Test Endpoints: {'✅' if endpoint_success else '❌'}")
        print(f"Comprehensive Tests: {'✅' if comprehensive_success else '❌'}")
        print(f"Overall Status: {'🎉 PASSED' if results['overall_success'] else '💥 FAILED'}")
        
        # Save results
        report_filename = f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_filename}")
        
        # Final status message
        if results["overall_success"]:
            print("\n🎉 BROWSER AUTOMATION SYSTEM VALIDATION PASSED!")
            print("✅ System is ready for production deployment")
            print("\n🔧 Validated Components:")
            print("   • Session Management and Optimization")
            print("   • LangGraph Hierarchical Workflows")
            print("   • Access Control and Quota Management")
            print("   • Performance Optimization")
            print("   • Error Handling and Fallbacks")
            print("   • API Endpoints and Integration")
            print("   • Test and Validation Framework")
        else:
            print("\n⚠️ BROWSER AUTOMATION SYSTEM VALIDATION FAILED!")
            print("❌ Review failed components before deployment")
            
            if not quick_success:
                print("   • Fix basic component issues first")
            if not endpoint_success:
                print("   • Review test endpoint functionality")
            if not comprehensive_success:
                print("   • Address comprehensive test failures")
        
        return results
        
    except Exception as e:
        print(f"\n💥 Validation suite failed: {e}")
        results["error"] = str(e)
        return results


def print_usage():
    """Print usage information"""
    print("🧪 Browser Automation Test Runner")
    print("=" * 35)
    print("\nUsage:")
    print("  python run_tests.py [option]")
    print("\nOptions:")
    print("  quick       - Run quick validation only")
    print("  endpoints   - Test API endpoints only")
    print("  comprehensive - Run comprehensive test suite")
    print("  all         - Run all validation tests (default)")
    print("  help        - Show this help message")
    print("\nExamples:")
    print("  python run_tests.py quick")
    print("  python run_tests.py all")


async def main():
    """Main function"""
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        option = sys.argv[1].lower()
    else:
        option = "all"
    
    if option == "help":
        print_usage()
        return
    
    try:
        if option == "quick":
            print("🔥 Running Quick Validation...")
            result = await run_quick_validation()
            success = result.get("overall_results", {}).get("status") == "PASSED"
            
        elif option == "endpoints":
            print("🧪 Testing API Endpoints...")
            result = await run_test_endpoint_demo()
            success = not result.get("error") and all(result.values())
            
        elif option == "comprehensive":
            print("🎯 Running Comprehensive Test Suite...")
            result = await run_comprehensive_tests()
            success = result.get("overall_results", {}).get("status") == "PASSED"
            
        elif option == "all":
            print("🚀 Running All Validation Tests...")
            result = await run_all_validation_tests()
            success = result.get("overall_success", False)
            
        else:
            print(f"❌ Unknown option: {option}")
            print_usage()
            sys.exit(1)
        
        # Exit with appropriate code
        if success:
            print(f"\n🎉 Test suite completed successfully!")
            sys.exit(0)
        else:
            print(f"\n💥 Test suite failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test runner failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("🧪 Browser Automation System Test Runner")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run the test suite
    asyncio.run(main())
