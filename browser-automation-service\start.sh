#!/bin/bash

# Rou<PERSON>ey Browser Automation Service Startup Script

echo "🚀 Starting RouKey Browser Automation Service..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.example .env
    echo "✅ Please edit .env file with your API keys and configuration"
    echo "📝 Required: OPENAI_API_KEY, GOOGLE_SEARCH_API_KEY, GOOGLE_SEARCH_ENGINE_ID"
    exit 1
fi

# Check if running in Docker
if [ "$1" = "docker" ]; then
    echo "🐳 Starting with Docker Compose..."
    docker-compose up -d
    echo "✅ Services started!"
    echo "📊 Health check: http://localhost:8000/health/"
    echo "📚 API docs: http://localhost:8000/docs"
    exit 0
fi

# Check Python version
python_version=$(python3 --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+')
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.11+ required. Found: $python_version"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

echo "🔧 Activating virtual environment..."
source venv/bin/activate

echo "📦 Installing dependencies..."
pip install -r requirements.txt

echo "🎭 Installing Playwright browsers..."
playwright install chromium --with-deps

# Check if Redis is running
if ! command -v redis-cli &> /dev/null; then
    echo "⚠️  Redis not found. Please install Redis or use Docker:"
    echo "   Docker: docker run -d -p 6379:6379 redis:7-alpine"
    echo "   Ubuntu: sudo apt install redis-server"
    echo "   macOS: brew install redis"
fi

# Check if Qdrant is running
if ! curl -s http://localhost:6333/health > /dev/null; then
    echo "⚠️  Qdrant not found. Please start Qdrant or use Docker:"
    echo "   Docker: docker run -d -p 6333:6333 qdrant/qdrant:latest"
fi

echo "🚀 Starting FastAPI server..."
uvicorn main:app --reload --host 0.0.0.0 --port 8000
