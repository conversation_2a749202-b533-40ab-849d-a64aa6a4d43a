"""
Comprehensive Test and Validation Runner
Complete testing suite for browser automation system and RouKey integration
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any
import aiohttp
import time

from app.api.test_endpoint import TestRequest, ValidationRequest, test_endpoint_api
from app.services.session_manager import session_manager
from app.services.performance_optimizer import performance_optimizer
from app.services.access_control import access_control_manager


class ComprehensiveTestRunner:
    """Comprehensive test runner for browser automation system"""
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.validation_results: Dict[str, Any] = {}
        self.start_time = None
        self.end_time = None
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all comprehensive tests"""
        
        print("🚀 Starting Comprehensive Browser Automation Test Suite")
        print("=" * 60)
        
        self.start_time = datetime.now()
        
        try:
            # Initialize all services
            await self._initialize_services()
            
            # Run test suites
            await self._run_browser_automation_tests()
            await self._run_langgraph_workflow_tests()
            await self._run_access_control_tests()
            await self._run_performance_tests()
            await self._run_integration_tests()
            
            # Run system validation
            await self._run_system_validation()
            
            # Generate comprehensive report
            final_report = await self._generate_final_report()
            
            self.end_time = datetime.now()
            
            print(f"\n⏰ Test suite completed in {(self.end_time - self.start_time).total_seconds():.1f} seconds")
            
            return final_report
            
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            return {"success": False, "error": str(e)}
        
        finally:
            # Cleanup
            await self._cleanup_services()
    
    async def _initialize_services(self):
        """Initialize all services for testing"""
        print("\n🔧 Initializing Services...")
        
        try:
            # Initialize session manager
            await session_manager.initialize()
            print("   ✅ Session Manager initialized")
            
            # Initialize performance optimizer
            await performance_optimizer.initialize()
            print("   ✅ Performance Optimizer initialized")
            
            # Initialize access control manager
            await access_control_manager.initialize()
            print("   ✅ Access Control Manager initialized")
            
            print("   🎯 All services initialized successfully")
            
        except Exception as e:
            print(f"   ❌ Service initialization failed: {e}")
            raise
    
    async def _run_browser_automation_tests(self):
        """Run browser automation tests"""
        print("\n🌐 Running Browser Automation Tests...")
        
        test_configs = [
            {
                "test_type": "basic_navigation",
                "test_config": {"test_url": "https://example.com"}
            },
            {
                "test_type": "data_extraction",
                "test_config": {"extract_type": "text_content"}
            },
            {
                "test_type": "form_interaction",
                "test_config": {"form_type": "search_form"}
            },
            {
                "test_type": "session_management",
                "test_config": {"concurrent_sessions": 3}
            }
        ]
        
        for i, config in enumerate(test_configs, 1):
            try:
                print(f"   {i}️⃣ Testing {config['test_type']}...")
                
                request = TestRequest(
                    test_type=config["test_type"],
                    user_id=f"test_user_{i}",
                    test_config=config["test_config"],
                    subscription_tier="pro",
                    timeout_seconds=60
                )
                
                # Execute test
                result = await test_endpoint_api._execute_browser_automation_test(
                    test_id=f"browser_test_{i}",
                    request=request
                )
                
                # Get result from test endpoint
                test_result = test_endpoint_api.test_results.get(f"browser_test_{i}")
                
                if test_result and test_result.success:
                    print(f"      ✅ {config['test_type']} passed")
                else:
                    print(f"      ❌ {config['test_type']} failed")
                
                self.test_results.append({
                    "category": "browser_automation",
                    "test_type": config["test_type"],
                    "success": test_result.success if test_result else False,
                    "execution_time": test_result.execution_time if test_result else 0,
                    "errors": test_result.errors if test_result else []
                })
                
            except Exception as e:
                print(f"      ❌ {config['test_type']} error: {e}")
                self.test_results.append({
                    "category": "browser_automation",
                    "test_type": config["test_type"],
                    "success": False,
                    "error": str(e)
                })
    
    async def _run_langgraph_workflow_tests(self):
        """Run LangGraph workflow tests"""
        print("\n🔗 Running LangGraph Workflow Tests...")
        
        test_configs = [
            {
                "test_type": "supervisor_agent",
                "test_config": {"workflow_type": "hierarchical"}
            },
            {
                "test_type": "planning_agent",
                "test_config": {"task_complexity": "medium"}
            },
            {
                "test_type": "execution_agent",
                "test_config": {"browser_actions": ["navigate", "extract"]}
            },
            {
                "test_type": "verification_agent",
                "test_config": {"verification_type": "google_search"}
            }
        ]
        
        for i, config in enumerate(test_configs, 1):
            try:
                print(f"   {i}️⃣ Testing {config['test_type']}...")
                
                request = TestRequest(
                    test_type=config["test_type"],
                    user_id=f"workflow_user_{i}",
                    test_config=config["test_config"],
                    subscription_tier="enterprise"
                )
                
                # Execute test
                result = await test_endpoint_api._execute_langgraph_workflow_test(
                    test_id=f"workflow_test_{i}",
                    request=request
                )
                
                test_result = test_endpoint_api.test_results.get(f"workflow_test_{i}")
                
                if test_result and test_result.success:
                    print(f"      ✅ {config['test_type']} passed")
                else:
                    print(f"      ❌ {config['test_type']} failed")
                
                self.test_results.append({
                    "category": "langgraph_workflow",
                    "test_type": config["test_type"],
                    "success": test_result.success if test_result else False,
                    "execution_time": test_result.execution_time if test_result else 0
                })
                
            except Exception as e:
                print(f"      ❌ {config['test_type']} error: {e}")
                self.test_results.append({
                    "category": "langgraph_workflow",
                    "test_type": config["test_type"],
                    "success": False,
                    "error": str(e)
                })
    
    async def _run_access_control_tests(self):
        """Run access control tests"""
        print("\n🔐 Running Access Control Tests...")
        
        test_configs = [
            {
                "test_type": "tier_validation",
                "subscription_tier": "starter",
                "expected_limits": {"browsing_tasks": 15}
            },
            {
                "test_type": "tier_validation",
                "subscription_tier": "pro",
                "expected_limits": {"browsing_tasks": 100}
            },
            {
                "test_type": "quota_enforcement",
                "subscription_tier": "starter",
                "test_scenario": "quota_exceeded"
            },
            {
                "test_type": "feature_access",
                "subscription_tier": "enterprise",
                "test_scenario": "all_features"
            }
        ]
        
        for i, config in enumerate(test_configs, 1):
            try:
                print(f"   {i}️⃣ Testing {config['test_type']} for {config['subscription_tier']}...")
                
                request = TestRequest(
                    test_type=config["test_type"],
                    user_id=f"access_user_{i}",
                    test_config=config,
                    subscription_tier=config["subscription_tier"]
                )
                
                # Execute test
                result = await test_endpoint_api._execute_access_control_test(
                    test_id=f"access_test_{i}",
                    request=request
                )
                
                test_result = test_endpoint_api.test_results.get(f"access_test_{i}")
                
                if test_result and test_result.success:
                    print(f"      ✅ {config['test_type']} passed for {config['subscription_tier']}")
                else:
                    print(f"      ❌ {config['test_type']} failed for {config['subscription_tier']}")
                
                self.test_results.append({
                    "category": "access_control",
                    "test_type": config["test_type"],
                    "subscription_tier": config["subscription_tier"],
                    "success": test_result.success if test_result else False
                })
                
            except Exception as e:
                print(f"      ❌ {config['test_type']} error: {e}")
                self.test_results.append({
                    "category": "access_control",
                    "test_type": config["test_type"],
                    "success": False,
                    "error": str(e)
                })
    
    async def _run_performance_tests(self):
        """Run performance tests"""
        print("\n⚡ Running Performance Tests...")
        
        test_configs = [
            {
                "test_type": "session_pool_performance",
                "test_config": {"concurrent_sessions": 5}
            },
            {
                "test_type": "resource_optimization",
                "test_config": {"optimization_strategy": "memory"}
            },
            {
                "test_type": "load_testing",
                "test_config": {"concurrent_requests": 10}
            },
            {
                "test_type": "performance_monitoring",
                "test_config": {"metrics_collection": True}
            }
        ]
        
        for i, config in enumerate(test_configs, 1):
            try:
                print(f"   {i}️⃣ Testing {config['test_type']}...")
                
                request = TestRequest(
                    test_type=config["test_type"],
                    user_id=f"perf_user_{i}",
                    test_config=config["test_config"]
                )
                
                # Execute test
                result = await test_endpoint_api._execute_performance_test(
                    test_id=f"perf_test_{i}",
                    request=request
                )
                
                test_result = test_endpoint_api.test_results.get(f"perf_test_{i}")
                
                if test_result and test_result.success:
                    print(f"      ✅ {config['test_type']} passed")
                else:
                    print(f"      ⚠️ {config['test_type']} had performance issues")
                
                self.test_results.append({
                    "category": "performance",
                    "test_type": config["test_type"],
                    "success": test_result.success if test_result else False,
                    "warnings": test_result.warnings if test_result else []
                })
                
            except Exception as e:
                print(f"      ❌ {config['test_type']} error: {e}")
                self.test_results.append({
                    "category": "performance",
                    "test_type": config["test_type"],
                    "success": False,
                    "error": str(e)
                })
    
    async def _run_integration_tests(self):
        """Run integration tests"""
        print("\n🔗 Running Integration Tests...")
        
        test_configs = [
            {
                "test_type": "end_to_end_workflow",
                "test_config": {"full_automation_flow": True}
            },
            {
                "test_type": "chat_to_browser_integration",
                "test_config": {"chat_intent": "browse_and_extract"}
            },
            {
                "test_type": "roukey_system_integration",
                "test_config": {"role_routing": True, "strategy_testing": True}
            }
        ]
        
        for i, config in enumerate(test_configs, 1):
            try:
                print(f"   {i}️⃣ Testing {config['test_type']}...")
                
                request = TestRequest(
                    test_type=config["test_type"],
                    user_id=f"integration_user_{i}",
                    test_config=config["test_config"],
                    subscription_tier="pro"
                )
                
                # Execute test
                result = await test_endpoint_api._execute_integration_test(
                    test_id=f"integration_test_{i}",
                    request=request
                )
                
                test_result = test_endpoint_api.test_results.get(f"integration_test_{i}")
                
                if test_result and test_result.success:
                    print(f"      ✅ {config['test_type']} passed")
                else:
                    print(f"      ❌ {config['test_type']} failed")
                
                self.test_results.append({
                    "category": "integration",
                    "test_type": config["test_type"],
                    "success": test_result.success if test_result else False
                })
                
            except Exception as e:
                print(f"      ❌ {config['test_type']} error: {e}")
                self.test_results.append({
                    "category": "integration",
                    "test_type": config["test_type"],
                    "success": False,
                    "error": str(e)
                })

    async def _run_system_validation(self):
        """Run comprehensive system validation"""
        print("\n🔍 Running System Validation...")

        validation_configs = [
            {
                "validation_scope": "quick",
                "include_performance": True,
                "include_security": True
            },
            {
                "validation_scope": "full",
                "include_performance": True,
                "include_security": True
            }
        ]

        for i, config in enumerate(validation_configs, 1):
            try:
                print(f"   {i}️⃣ Running {config['validation_scope']} validation...")

                request = ValidationRequest(**config)

                # Execute validation
                validation_result = await test_endpoint_api._execute_system_validation(request)

                if validation_result.get("success", False):
                    print(f"      ✅ {config['validation_scope']} validation passed")
                else:
                    print(f"      ❌ {config['validation_scope']} validation failed")

                self.validation_results[config["validation_scope"]] = validation_result

            except Exception as e:
                print(f"      ❌ {config['validation_scope']} validation error: {e}")
                self.validation_results[config["validation_scope"]] = {
                    "success": False,
                    "error": str(e)
                }

    async def _generate_final_report(self) -> Dict[str, Any]:
        """Generate comprehensive final report"""
        print("\n📊 Generating Final Report...")

        # Calculate statistics
        total_tests = len(self.test_results)
        successful_tests = sum(1 for test in self.test_results if test.get("success", False))
        failed_tests = total_tests - successful_tests

        # Group results by category
        results_by_category = {}
        for test in self.test_results:
            category = test.get("category", "unknown")
            if category not in results_by_category:
                results_by_category[category] = []
            results_by_category[category].append(test)

        # Calculate category statistics
        category_stats = {}
        for category, tests in results_by_category.items():
            category_stats[category] = {
                "total": len(tests),
                "successful": sum(1 for test in tests if test.get("success", False)),
                "failed": sum(1 for test in tests if not test.get("success", False)),
                "success_rate": (sum(1 for test in tests if test.get("success", False)) / len(tests)) * 100
            }

        # Overall success rate
        overall_success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0

        # Determine overall status
        overall_status = "PASSED" if overall_success_rate >= 80 else "FAILED"

        # Generate recommendations
        recommendations = []

        if overall_success_rate < 100:
            recommendations.append("Review and fix failing test cases")

        if category_stats.get("performance", {}).get("success_rate", 100) < 90:
            recommendations.append("Optimize system performance")

        if category_stats.get("integration", {}).get("success_rate", 100) < 95:
            recommendations.append("Review integration configurations")

        if not self.validation_results.get("full", {}).get("success", False):
            recommendations.append("Address system validation issues")

        # Create final report
        final_report = {
            "test_suite_info": {
                "name": "Browser Automation Comprehensive Test Suite",
                "version": "1.0.0",
                "execution_time": (self.end_time - self.start_time).total_seconds() if self.end_time else 0,
                "timestamp": datetime.now().isoformat()
            },
            "overall_results": {
                "status": overall_status,
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": overall_success_rate
            },
            "category_results": category_stats,
            "detailed_results": results_by_category,
            "validation_results": self.validation_results,
            "recommendations": recommendations,
            "system_info": {
                "browser_automation_service": "operational",
                "langgraph_workflow": "operational",
                "session_management": "operational",
                "performance_optimization": "operational",
                "access_control": "operational"
            }
        }

        # Print summary
        print(f"\n📈 Test Suite Summary:")
        print(f"   Overall Status: {overall_status}")
        print(f"   Success Rate: {overall_success_rate:.1f}%")
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {successful_tests}")
        print(f"   Failed: {failed_tests}")

        print(f"\n📊 Category Breakdown:")
        for category, stats in category_stats.items():
            print(f"   {category.title()}: {stats['successful']}/{stats['total']} ({stats['success_rate']:.1f}%)")

        if recommendations:
            print(f"\n💡 Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")

        return final_report

    async def _cleanup_services(self):
        """Cleanup all services"""
        print("\n🧹 Cleaning up services...")

        try:
            # Shutdown session manager
            await session_manager.shutdown()
            print("   ✅ Session Manager shutdown")

            # Shutdown performance optimizer
            await performance_optimizer.shutdown()
            print("   ✅ Performance Optimizer shutdown")

            print("   🎯 All services cleaned up successfully")

        except Exception as e:
            print(f"   ⚠️ Cleanup warning: {e}")


async def run_comprehensive_tests():
    """Main function to run comprehensive tests"""

    print("🎯 Browser Automation System - Comprehensive Test Suite")
    print("🔧 Testing all components, integrations, and performance")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # Create test runner
        test_runner = ComprehensiveTestRunner()

        # Run all tests
        final_report = await test_runner.run_all_tests()

        # Save report to file
        report_filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(report_filename, 'w') as f:
            json.dump(final_report, f, indent=2, default=str)

        print(f"\n📄 Detailed report saved to: {report_filename}")

        # Print final status
        if final_report.get("overall_results", {}).get("status") == "PASSED":
            print("\n🎉 COMPREHENSIVE TEST SUITE PASSED!")
            print("✅ Browser automation system is ready for production")
        else:
            print("\n⚠️ COMPREHENSIVE TEST SUITE FAILED!")
            print("❌ Review failed tests and address issues before deployment")

        return final_report

    except Exception as e:
        print(f"\n💥 Test suite execution failed: {e}")
        return {"success": False, "error": str(e)}


if __name__ == "__main__":
    print("🚀 Starting Browser Automation Comprehensive Test Suite...")

    # Run the comprehensive test suite
    asyncio.run(run_comprehensive_tests())
