"""
Test script for RouKey Browser Automation Service
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_browser_automation_service():
    """Test the browser automation service endpoints"""
    
    base_url = "http://localhost:8000"
    
    print("🧪 Testing RouKey Browser Automation Service")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        
        # Test 1: Health Check
        print("\n1️⃣ Testing Health Check...")
        try:
            response = await client.get(f"{base_url}/health/")
            if response.status_code == 200:
                print("✅ Health check passed")
                print(f"   Status: {response.json()['status']}")
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return
        
        # Test 2: Detailed Health Check
        print("\n2️⃣ Testing Detailed Health Check...")
        try:
            response = await client.get(f"{base_url}/api/v1/test/health-detailed")
            if response.status_code == 200:
                health_data = response.json()
                print("✅ Detailed health check passed")
                print(f"   Overall status: {health_data['overall_status']}")
                for component, status in health_data['components'].items():
                    print(f"   {component}: {status['status']}")
            else:
                print(f"❌ Detailed health check failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Detailed health check error: {e}")
        
        # Test 3: Task Simulation
        print("\n3️⃣ Testing Task Simulation...")
        try:
            test_task = "Find the cheapest laptop on Amazon under $500"
            response = await client.post(
                f"{base_url}/api/v1/test/simulate-task",
                params={"task_description": test_task}
            )
            if response.status_code == 200:
                simulation_data = response.json()
                print("✅ Task simulation passed")
                print(f"   Task: {simulation_data['task_description']}")
                print(f"   Estimated steps: {simulation_data.get('estimated_steps', 'N/A')}")
                print(f"   Complexity: {simulation_data.get('estimated_complexity', 'N/A')}")
                print(f"   Required agents: {simulation_data.get('required_agents', [])}")
            else:
                print(f"❌ Task simulation failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Task simulation error: {e}")
        
        # Test 4: Browser Automation Test (Simple)
        print("\n4️⃣ Testing Browser Automation (Simple Task)...")
        try:
            test_request = {
                "task": "Navigate to google.com and get the page title",
                "user_tier": "pro",
                "enable_verification": False,
                "max_steps": 5
            }
            
            print(f"   Sending request: {test_request['task']}")
            response = await client.post(
                f"{base_url}/api/v1/test/browser-automation",
                json=test_request
            )
            
            if response.status_code == 200:
                test_data = response.json()
                print("✅ Browser automation test passed")
                print(f"   Success: {test_data['success']}")
                print(f"   Message: {test_data['message']}")
                
                if test_data.get('test_results'):
                    results = test_data['test_results']
                    print(f"   Steps executed: {results.get('steps_executed', 0)}")
                    print(f"   Todo items: {results.get('todo_items', 0)}")
                    print(f"   Completed todos: {results.get('completed_todos', 0)}")
                    print(f"   Execution time: {results.get('execution_time_ms', 0)}ms")
            else:
                print(f"❌ Browser automation test failed: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"❌ Browser automation test error: {e}")
        
        # Test 5: Browser Automation Test (Complex Task)
        print("\n5️⃣ Testing Browser Automation (Complex Task)...")
        try:
            complex_request = {
                "task": "Search for 'best programming languages 2024' and summarize the top 3 results",
                "user_tier": "pro",
                "enable_verification": True,
                "max_steps": 15
            }
            
            print(f"   Sending request: {complex_request['task']}")
            response = await client.post(
                f"{base_url}/api/v1/test/browser-automation",
                json=complex_request
            )
            
            if response.status_code == 200:
                test_data = response.json()
                print("✅ Complex browser automation test passed")
                print(f"   Success: {test_data['success']}")
                print(f"   Message: {test_data['message']}")
                
                if test_data.get('test_results'):
                    results = test_data['test_results']
                    print(f"   Steps executed: {results.get('steps_executed', 0)}")
                    print(f"   Verification enabled: {results.get('verification_enabled', False)}")
                    print(f"   Verification results: {results.get('verification_results', False)}")
            else:
                print(f"❌ Complex browser automation test failed: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"❌ Complex browser automation test error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Browser Automation Service Testing Complete!")
    print("\n📚 Next steps:")
    print("   1. Check API documentation: http://localhost:8000/docs")
    print("   2. Test with RouKey frontend integration")
    print("   3. Configure your API keys in .env file")
    print("   4. Set up Redis and Qdrant for full functionality")


async def test_individual_endpoint(endpoint: str, method: str = "GET", data: dict = None):
    """Test an individual endpoint"""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            if method.upper() == "GET":
                response = await client.get(f"{base_url}{endpoint}")
            elif method.upper() == "POST":
                response = await client.post(f"{base_url}{endpoint}", json=data)
            
            print(f"🔍 Testing {method} {endpoint}")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
        except Exception as e:
            print(f"❌ Error testing {endpoint}: {e}")


if __name__ == "__main__":
    print("🚀 RouKey Browser Automation Service Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if service is running
    try:
        import httpx
        response = httpx.get("http://localhost:8000/health/", timeout=5.0)
        if response.status_code != 200:
            print("❌ Service not running. Please start the service first:")
            print("   Windows: start.bat")
            print("   Docker: start.bat docker")
            exit(1)
    except Exception:
        print("❌ Service not running. Please start the service first:")
        print("   Windows: start.bat")
        print("   Docker: start.bat docker")
        exit(1)
    
    # Run tests
    asyncio.run(test_browser_automation_service())
