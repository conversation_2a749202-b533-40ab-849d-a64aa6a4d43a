/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/test/semantic-cache/route";
exports.ids = ["app/api/test/semantic-cache/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_test_semantic_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/test/semantic-cache/route.ts */ \"(rsc)/./src/app/api/test/semantic-cache/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/test/semantic-cache/route\",\n        pathname: \"/api/test/semantic-cache\",\n        filename: \"route\",\n        bundlePath: \"app/api/test/semantic-cache/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\test\\\\semantic-cache\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_test_semantic_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/test/semantic-cache/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/test/semantic-cache/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/semantic-cache/SemanticCacheService */ \"(rsc)/./src/lib/semantic-cache/SemanticCacheService.ts\");\n/* harmony import */ var _lib_semantic_cache_tierUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/semantic-cache/tierUtils */ \"(rsc)/./src/lib/semantic-cache/tierUtils.ts\");\n/**\n * Test endpoint for Semantic Cache functionality\n * This endpoint allows testing the semantic cache without going through the full chat completions flow\n */ \n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, userId, configId, promptText, responseData } = body;\n        if (!userId || !configId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'userId and configId are required'\n            }, {\n                status: 400\n            });\n        }\n        const userTier = await (0,_lib_semantic_cache_tierUtils__WEBPACK_IMPORTED_MODULE_2__.getUserTier)(userId);\n        if (action === 'search') {\n            if (!promptText) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'promptText is required for search'\n                }, {\n                    status: 400\n                });\n            }\n            console.log(`[Semantic Cache Test] Searching cache for user ${userId} (tier: ${userTier})`);\n            const cacheHit = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.searchCache({\n                promptText,\n                modelUsed: 'test-model',\n                providerUsed: 'test-provider',\n                temperature: 0.7,\n                maxTokens: 1000,\n                metadata: {\n                    test: true\n                }\n            }, userId, configId, userTier);\n            if (cacheHit) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    cacheHit: true,\n                    data: {\n                        similarity: cacheHit.similarity,\n                        promptText: cacheHit.promptText,\n                        responseData: cacheHit.responseData,\n                        hitCount: cacheHit.hitCount,\n                        createdAt: cacheHit.createdAt\n                    }\n                });\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    cacheHit: false,\n                    message: 'No cache hit found'\n                });\n            }\n        }\n        if (action === 'store') {\n            if (!promptText || !responseData) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'promptText and responseData are required for store'\n                }, {\n                    status: 400\n                });\n            }\n            console.log(`[Semantic Cache Test] Storing cache for user ${userId} (tier: ${userTier})`);\n            const stored = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.storeCache({\n                promptText,\n                modelUsed: 'test-model',\n                providerUsed: 'test-provider',\n                temperature: 0.7,\n                maxTokens: 1000,\n                metadata: {\n                    test: true\n                }\n            }, {\n                responseData,\n                tokensPrompt: 100,\n                tokensCompletion: 200,\n                cost: 0.001\n            }, userId, configId, userTier);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                stored,\n                message: stored ? 'Response stored in cache' : 'Failed to store in cache (tier restrictions or error)'\n            });\n        }\n        if (action === 'stats') {\n            console.log(`[Semantic Cache Test] Getting stats for user ${userId}`);\n            const stats = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.getCacheStats(userId, configId, 7);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                stats,\n                userTier\n            });\n        }\n        if (action === 'cleanup') {\n            console.log(`[Semantic Cache Test] Running cleanup`);\n            const deletedCount = await _lib_semantic_cache_SemanticCacheService__WEBPACK_IMPORTED_MODULE_1__.semanticCache.cleanupExpiredCache();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                deletedCount,\n                message: `Cleaned up ${deletedCount} expired cache entries`\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid action. Use: search, store, stats, or cleanup'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('[Semantic Cache Test] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Semantic Cache Test Endpoint',\n        usage: {\n            'POST /api/test/semantic-cache': {\n                description: 'Test semantic cache functionality',\n                actions: {\n                    search: 'Search for cached responses',\n                    store: 'Store a response in cache',\n                    stats: 'Get cache statistics',\n                    cleanup: 'Clean up expired cache entries'\n                },\n                example: {\n                    action: 'search',\n                    userId: 'user-uuid',\n                    configId: 'config-uuid',\n                    promptText: 'Hello, how are you?'\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS90ZXN0L3NlbWFudGljLWNhY2hlL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBRXVEO0FBQzBDO0FBQ3JDO0FBRXRELGVBQWVJLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNQyxPQUFPLE1BQU1ELFFBQVFFLElBQUk7UUFDL0IsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxVQUFVLEVBQUVDLFlBQVksRUFBRSxHQUFHTjtRQUUvRCxJQUFJLENBQUNHLFVBQVUsQ0FBQ0MsVUFBVTtZQUN4QixPQUFPVixxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO2dCQUFFTSxPQUFPO1lBQW1DLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUN4RjtRQUVBLE1BQU1DLFdBQVcsTUFBTVosMEVBQVdBLENBQUNNO1FBRW5DLElBQUlELFdBQVcsVUFBVTtZQUN2QixJQUFJLENBQUNHLFlBQVk7Z0JBQ2YsT0FBT1gscURBQVlBLENBQUNPLElBQUksQ0FBQztvQkFBRU0sT0FBTztnQkFBb0MsR0FBRztvQkFBRUMsUUFBUTtnQkFBSTtZQUN6RjtZQUVBRSxRQUFRQyxHQUFHLENBQUMsQ0FBQywrQ0FBK0MsRUFBRVIsT0FBTyxRQUFRLEVBQUVNLFNBQVMsQ0FBQyxDQUFDO1lBRTFGLE1BQU1HLFdBQVcsTUFBTWhCLG1GQUFvQkEsQ0FBQ2lCLFdBQVcsQ0FDckQ7Z0JBQ0VSO2dCQUNBUyxXQUFXO2dCQUNYQyxjQUFjO2dCQUNkQyxhQUFhO2dCQUNiQyxXQUFXO2dCQUNYQyxVQUFVO29CQUFFQyxNQUFNO2dCQUFLO1lBQ3pCLEdBQ0FoQixRQUNBQyxVQUNBSztZQUdGLElBQUlHLFVBQVU7Z0JBQ1osT0FBT2xCLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7b0JBQ3ZCbUIsU0FBUztvQkFDVFIsVUFBVTtvQkFDVlMsTUFBTTt3QkFDSkMsWUFBWVYsU0FBU1UsVUFBVTt3QkFDL0JqQixZQUFZTyxTQUFTUCxVQUFVO3dCQUMvQkMsY0FBY00sU0FBU04sWUFBWTt3QkFDbkNpQixVQUFVWCxTQUFTVyxRQUFRO3dCQUMzQkMsV0FBV1osU0FBU1ksU0FBUztvQkFDL0I7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMLE9BQU85QixxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO29CQUN2Qm1CLFNBQVM7b0JBQ1RSLFVBQVU7b0JBQ1ZhLFNBQVM7Z0JBQ1g7WUFDRjtRQUNGO1FBRUEsSUFBSXZCLFdBQVcsU0FBUztZQUN0QixJQUFJLENBQUNHLGNBQWMsQ0FBQ0MsY0FBYztnQkFDaEMsT0FBT1oscURBQVlBLENBQUNPLElBQUksQ0FBQztvQkFBRU0sT0FBTztnQkFBcUQsR0FBRztvQkFBRUMsUUFBUTtnQkFBSTtZQUMxRztZQUVBRSxRQUFRQyxHQUFHLENBQUMsQ0FBQyw2Q0FBNkMsRUFBRVIsT0FBTyxRQUFRLEVBQUVNLFNBQVMsQ0FBQyxDQUFDO1lBRXhGLE1BQU1pQixTQUFTLE1BQU05QixtRkFBb0JBLENBQUMrQixVQUFVLENBQ2xEO2dCQUNFdEI7Z0JBQ0FTLFdBQVc7Z0JBQ1hDLGNBQWM7Z0JBQ2RDLGFBQWE7Z0JBQ2JDLFdBQVc7Z0JBQ1hDLFVBQVU7b0JBQUVDLE1BQU07Z0JBQUs7WUFDekIsR0FDQTtnQkFDRWI7Z0JBQ0FzQixjQUFjO2dCQUNkQyxrQkFBa0I7Z0JBQ2xCQyxNQUFNO1lBQ1IsR0FDQTNCLFFBQ0FDLFVBQ0FLO1lBR0YsT0FBT2YscURBQVlBLENBQUNPLElBQUksQ0FBQztnQkFDdkJtQixTQUFTO2dCQUNUTTtnQkFDQUQsU0FBU0MsU0FBUyw2QkFBNkI7WUFDakQ7UUFDRjtRQUVBLElBQUl4QixXQUFXLFNBQVM7WUFDdEJRLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDZDQUE2QyxFQUFFUixRQUFRO1lBRXBFLE1BQU00QixRQUFRLE1BQU1uQyxtRkFBb0JBLENBQUNvQyxhQUFhLENBQUM3QixRQUFRQyxVQUFVO1lBRXpFLE9BQU9WLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7Z0JBQ3ZCbUIsU0FBUztnQkFDVFc7Z0JBQ0F0QjtZQUNGO1FBQ0Y7UUFFQSxJQUFJUCxXQUFXLFdBQVc7WUFDeEJRLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHFDQUFxQyxDQUFDO1lBRW5ELE1BQU1zQixlQUFlLE1BQU1yQyxtRkFBb0JBLENBQUNzQyxtQkFBbUI7WUFFbkUsT0FBT3hDLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7Z0JBQ3ZCbUIsU0FBUztnQkFDVGE7Z0JBQ0FSLFNBQVMsQ0FBQyxXQUFXLEVBQUVRLGFBQWEsc0JBQXNCLENBQUM7WUFDN0Q7UUFDRjtRQUVBLE9BQU92QyxxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO1lBQUVNLE9BQU87UUFBd0QsR0FBRztZQUFFQyxRQUFRO1FBQUk7SUFFN0csRUFBRSxPQUFPRCxPQUFZO1FBQ25CRyxRQUFRSCxLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPYixxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO1lBQ3ZCTSxPQUFPO1lBQ1A0QixTQUFTNUIsTUFBTWtCLE9BQU87UUFDeEIsR0FBRztZQUFFakIsUUFBUTtRQUFJO0lBQ25CO0FBQ0Y7QUFFTyxlQUFlNEIsSUFBSXJDLE9BQW9CO0lBQzVDLE9BQU9MLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7UUFDdkJ3QixTQUFTO1FBQ1RZLE9BQU87WUFDTCxpQ0FBaUM7Z0JBQy9CQyxhQUFhO2dCQUNiQyxTQUFTO29CQUNQQyxRQUFRO29CQUNSQyxPQUFPO29CQUNQVixPQUFPO29CQUNQVyxTQUFTO2dCQUNYO2dCQUNBQyxTQUFTO29CQUNQekMsUUFBUTtvQkFDUkMsUUFBUTtvQkFDUkMsVUFBVTtvQkFDVkMsWUFBWTtnQkFDZDtZQUNGO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxhcGlcXHRlc3RcXHNlbWFudGljLWNhY2hlXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRlc3QgZW5kcG9pbnQgZm9yIFNlbWFudGljIENhY2hlIGZ1bmN0aW9uYWxpdHlcbiAqIFRoaXMgZW5kcG9pbnQgYWxsb3dzIHRlc3RpbmcgdGhlIHNlbWFudGljIGNhY2hlIHdpdGhvdXQgZ29pbmcgdGhyb3VnaCB0aGUgZnVsbCBjaGF0IGNvbXBsZXRpb25zIGZsb3dcbiAqL1xuXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgc2VtYW50aWNDYWNoZSBhcyBzZW1hbnRpY0NhY2hlU2VydmljZSB9IGZyb20gJ0AvbGliL3NlbWFudGljLWNhY2hlL1NlbWFudGljQ2FjaGVTZXJ2aWNlJztcbmltcG9ydCB7IGdldFVzZXJUaWVyIH0gZnJvbSAnQC9saWIvc2VtYW50aWMtY2FjaGUvdGllclV0aWxzJztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgY29uc3QgeyBhY3Rpb24sIHVzZXJJZCwgY29uZmlnSWQsIHByb21wdFRleHQsIHJlc3BvbnNlRGF0YSB9ID0gYm9keTtcblxuICAgIGlmICghdXNlcklkIHx8ICFjb25maWdJZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICd1c2VySWQgYW5kIGNvbmZpZ0lkIGFyZSByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICBjb25zdCB1c2VyVGllciA9IGF3YWl0IGdldFVzZXJUaWVyKHVzZXJJZCk7XG5cbiAgICBpZiAoYWN0aW9uID09PSAnc2VhcmNoJykge1xuICAgICAgaWYgKCFwcm9tcHRUZXh0KSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAncHJvbXB0VGV4dCBpcyByZXF1aXJlZCBmb3Igc2VhcmNoJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhgW1NlbWFudGljIENhY2hlIFRlc3RdIFNlYXJjaGluZyBjYWNoZSBmb3IgdXNlciAke3VzZXJJZH0gKHRpZXI6ICR7dXNlclRpZXJ9KWApO1xuICAgICAgXG4gICAgICBjb25zdCBjYWNoZUhpdCA9IGF3YWl0IHNlbWFudGljQ2FjaGVTZXJ2aWNlLnNlYXJjaENhY2hlKFxuICAgICAgICB7XG4gICAgICAgICAgcHJvbXB0VGV4dCxcbiAgICAgICAgICBtb2RlbFVzZWQ6ICd0ZXN0LW1vZGVsJyxcbiAgICAgICAgICBwcm92aWRlclVzZWQ6ICd0ZXN0LXByb3ZpZGVyJyxcbiAgICAgICAgICB0ZW1wZXJhdHVyZTogMC43LFxuICAgICAgICAgIG1heFRva2VuczogMTAwMCxcbiAgICAgICAgICBtZXRhZGF0YTogeyB0ZXN0OiB0cnVlIH1cbiAgICAgICAgfSxcbiAgICAgICAgdXNlcklkLFxuICAgICAgICBjb25maWdJZCxcbiAgICAgICAgdXNlclRpZXJcbiAgICAgICk7XG5cbiAgICAgIGlmIChjYWNoZUhpdCkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgY2FjaGVIaXQ6IHRydWUsXG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgc2ltaWxhcml0eTogY2FjaGVIaXQuc2ltaWxhcml0eSxcbiAgICAgICAgICAgIHByb21wdFRleHQ6IGNhY2hlSGl0LnByb21wdFRleHQsXG4gICAgICAgICAgICByZXNwb25zZURhdGE6IGNhY2hlSGl0LnJlc3BvbnNlRGF0YSxcbiAgICAgICAgICAgIGhpdENvdW50OiBjYWNoZUhpdC5oaXRDb3VudCxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogY2FjaGVIaXQuY3JlYXRlZEF0XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBjYWNoZUhpdDogZmFsc2UsXG4gICAgICAgICAgbWVzc2FnZTogJ05vIGNhY2hlIGhpdCBmb3VuZCdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGFjdGlvbiA9PT0gJ3N0b3JlJykge1xuICAgICAgaWYgKCFwcm9tcHRUZXh0IHx8ICFyZXNwb25zZURhdGEpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdwcm9tcHRUZXh0IGFuZCByZXNwb25zZURhdGEgYXJlIHJlcXVpcmVkIGZvciBzdG9yZScgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYFtTZW1hbnRpYyBDYWNoZSBUZXN0XSBTdG9yaW5nIGNhY2hlIGZvciB1c2VyICR7dXNlcklkfSAodGllcjogJHt1c2VyVGllcn0pYCk7XG5cbiAgICAgIGNvbnN0IHN0b3JlZCA9IGF3YWl0IHNlbWFudGljQ2FjaGVTZXJ2aWNlLnN0b3JlQ2FjaGUoXG4gICAgICAgIHtcbiAgICAgICAgICBwcm9tcHRUZXh0LFxuICAgICAgICAgIG1vZGVsVXNlZDogJ3Rlc3QtbW9kZWwnLFxuICAgICAgICAgIHByb3ZpZGVyVXNlZDogJ3Rlc3QtcHJvdmlkZXInLFxuICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjcsXG4gICAgICAgICAgbWF4VG9rZW5zOiAxMDAwLFxuICAgICAgICAgIG1ldGFkYXRhOiB7IHRlc3Q6IHRydWUgfVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgcmVzcG9uc2VEYXRhLFxuICAgICAgICAgIHRva2Vuc1Byb21wdDogMTAwLFxuICAgICAgICAgIHRva2Vuc0NvbXBsZXRpb246IDIwMCxcbiAgICAgICAgICBjb3N0OiAwLjAwMVxuICAgICAgICB9LFxuICAgICAgICB1c2VySWQsXG4gICAgICAgIGNvbmZpZ0lkLFxuICAgICAgICB1c2VyVGllclxuICAgICAgKTtcblxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgc3RvcmVkLFxuICAgICAgICBtZXNzYWdlOiBzdG9yZWQgPyAnUmVzcG9uc2Ugc3RvcmVkIGluIGNhY2hlJyA6ICdGYWlsZWQgdG8gc3RvcmUgaW4gY2FjaGUgKHRpZXIgcmVzdHJpY3Rpb25zIG9yIGVycm9yKSdcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGlmIChhY3Rpb24gPT09ICdzdGF0cycpIHtcbiAgICAgIGNvbnNvbGUubG9nKGBbU2VtYW50aWMgQ2FjaGUgVGVzdF0gR2V0dGluZyBzdGF0cyBmb3IgdXNlciAke3VzZXJJZH1gKTtcblxuICAgICAgY29uc3Qgc3RhdHMgPSBhd2FpdCBzZW1hbnRpY0NhY2hlU2VydmljZS5nZXRDYWNoZVN0YXRzKHVzZXJJZCwgY29uZmlnSWQsIDcpO1xuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBzdGF0cyxcbiAgICAgICAgdXNlclRpZXJcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGlmIChhY3Rpb24gPT09ICdjbGVhbnVwJykge1xuICAgICAgY29uc29sZS5sb2coYFtTZW1hbnRpYyBDYWNoZSBUZXN0XSBSdW5uaW5nIGNsZWFudXBgKTtcblxuICAgICAgY29uc3QgZGVsZXRlZENvdW50ID0gYXdhaXQgc2VtYW50aWNDYWNoZVNlcnZpY2UuY2xlYW51cEV4cGlyZWRDYWNoZSgpO1xuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBkZWxldGVkQ291bnQsXG4gICAgICAgIG1lc3NhZ2U6IGBDbGVhbmVkIHVwICR7ZGVsZXRlZENvdW50fSBleHBpcmVkIGNhY2hlIGVudHJpZXNgXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0ludmFsaWQgYWN0aW9uLiBVc2U6IHNlYXJjaCwgc3RvcmUsIHN0YXRzLCBvciBjbGVhbnVwJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdbU2VtYW50aWMgQ2FjaGUgVGVzdF0gRXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IFxuICAgICAgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InLCBcbiAgICAgIGRldGFpbHM6IGVycm9yLm1lc3NhZ2UgXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgbWVzc2FnZTogJ1NlbWFudGljIENhY2hlIFRlc3QgRW5kcG9pbnQnLFxuICAgIHVzYWdlOiB7XG4gICAgICAnUE9TVCAvYXBpL3Rlc3Qvc2VtYW50aWMtY2FjaGUnOiB7XG4gICAgICAgIGRlc2NyaXB0aW9uOiAnVGVzdCBzZW1hbnRpYyBjYWNoZSBmdW5jdGlvbmFsaXR5JyxcbiAgICAgICAgYWN0aW9uczoge1xuICAgICAgICAgIHNlYXJjaDogJ1NlYXJjaCBmb3IgY2FjaGVkIHJlc3BvbnNlcycsXG4gICAgICAgICAgc3RvcmU6ICdTdG9yZSBhIHJlc3BvbnNlIGluIGNhY2hlJyxcbiAgICAgICAgICBzdGF0czogJ0dldCBjYWNoZSBzdGF0aXN0aWNzJyxcbiAgICAgICAgICBjbGVhbnVwOiAnQ2xlYW4gdXAgZXhwaXJlZCBjYWNoZSBlbnRyaWVzJ1xuICAgICAgICB9LFxuICAgICAgICBleGFtcGxlOiB7XG4gICAgICAgICAgYWN0aW9uOiAnc2VhcmNoJyxcbiAgICAgICAgICB1c2VySWQ6ICd1c2VyLXV1aWQnLFxuICAgICAgICAgIGNvbmZpZ0lkOiAnY29uZmlnLXV1aWQnLFxuICAgICAgICAgIHByb21wdFRleHQ6ICdIZWxsbywgaG93IGFyZSB5b3U/J1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9KTtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJzZW1hbnRpY0NhY2hlIiwic2VtYW50aWNDYWNoZVNlcnZpY2UiLCJnZXRVc2VyVGllciIsIlBPU1QiLCJyZXF1ZXN0IiwiYm9keSIsImpzb24iLCJhY3Rpb24iLCJ1c2VySWQiLCJjb25maWdJZCIsInByb21wdFRleHQiLCJyZXNwb25zZURhdGEiLCJlcnJvciIsInN0YXR1cyIsInVzZXJUaWVyIiwiY29uc29sZSIsImxvZyIsImNhY2hlSGl0Iiwic2VhcmNoQ2FjaGUiLCJtb2RlbFVzZWQiLCJwcm92aWRlclVzZWQiLCJ0ZW1wZXJhdHVyZSIsIm1heFRva2VucyIsIm1ldGFkYXRhIiwidGVzdCIsInN1Y2Nlc3MiLCJkYXRhIiwic2ltaWxhcml0eSIsImhpdENvdW50IiwiY3JlYXRlZEF0IiwibWVzc2FnZSIsInN0b3JlZCIsInN0b3JlQ2FjaGUiLCJ0b2tlbnNQcm9tcHQiLCJ0b2tlbnNDb21wbGV0aW9uIiwiY29zdCIsInN0YXRzIiwiZ2V0Q2FjaGVTdGF0cyIsImRlbGV0ZWRDb3VudCIsImNsZWFudXBFeHBpcmVkQ2FjaGUiLCJkZXRhaWxzIiwiR0VUIiwidXNhZ2UiLCJkZXNjcmlwdGlvbiIsImFjdGlvbnMiLCJzZWFyY2giLCJzdG9yZSIsImNsZWFudXAiLCJleGFtcGxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/test/semantic-cache/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/embeddings/jina.ts":
/*!************************************!*\
  !*** ./src/lib/embeddings/jina.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiKeyJinaEmbeddings: () => (/* binding */ MultiKeyJinaEmbeddings),\n/* harmony export */   jinaEmbeddings: () => (/* binding */ jinaEmbeddings)\n/* harmony export */ });\n/**\r\n * Multi-Key Jina Embeddings v3 Implementation\r\n * Provides automatic key rotation and high rate limits for RouKey\r\n */ class MultiKeyJinaEmbeddings {\n    constructor(){\n        this.currentKeyIndex = 0;\n        this.keyUsage = new Map();\n        this.baseUrl = 'https://api.jina.ai/v1/embeddings';\n        this.model = 'jina-embeddings-v3';\n        // Load all Jina API keys from environment\n        this.apiKeys = [\n            process.env.JINA_API_KEY,\n            process.env.JINA_API_KEY_2,\n            process.env.JINA_API_KEY_3,\n            process.env.JINA_API_KEY_4,\n            process.env.JINA_API_KEY_5,\n            process.env.JINA_API_KEY_6,\n            process.env.JINA_API_KEY_7,\n            process.env.JINA_API_KEY_9,\n            process.env.JINA_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Jina API keys found in environment variables');\n        }\n        console.log(`[Jina Embeddings] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage stats for each key\n        this.apiKeys.forEach((key)=>{\n            this.keyUsage.set(key, {\n                requests: 0,\n                tokens: 0,\n                lastUsed: new Date(),\n                errors: 0\n            });\n        });\n    }\n    /**\r\n   * Get the next API key using round-robin rotation\r\n   */ getNextKey() {\n        const key = this.apiKeys[this.currentKeyIndex];\n        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;\n        return key;\n    }\n    /**\r\n   * Get the best available API key based on usage and error rates\r\n   */ getBestKey() {\n        // For now, use simple round-robin\n        // TODO: Implement smart selection based on usage stats\n        return this.getNextKey();\n    }\n    /**\r\n   * Update usage statistics for a key\r\n   */ updateKeyUsage(apiKey, tokens, isError = false) {\n        const stats = this.keyUsage.get(apiKey);\n        if (stats) {\n            stats.requests++;\n            stats.tokens += tokens;\n            stats.lastUsed = new Date();\n            if (isError) {\n                stats.errors++;\n                stats.lastError = new Date();\n            }\n        }\n    }\n    /**\r\n   * Generate embedding for a single text input\r\n   */ async embedQuery(text) {\n        const maxRetries = this.apiKeys.length;\n        let lastError = null;\n        for(let attempt = 0; attempt < maxRetries; attempt++){\n            try {\n                const apiKey = this.getBestKey();\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1}/${maxRetries} with key ${this.apiKeys.indexOf(apiKey) + 1}`);\n                const response = await fetch(this.baseUrl, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        model: this.model,\n                        input: [\n                            text\n                        ],\n                        normalized: true,\n                        embedding_type: 'float'\n                    })\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    if (response.status === 429) {\n                        console.log(`[Jina Embeddings] Rate limit hit for key ${this.apiKeys.indexOf(apiKey) + 1}, trying next key...`);\n                        this.updateKeyUsage(apiKey, 0, true);\n                        continue;\n                    }\n                    throw new Error(`HTTP ${response.status}: ${errorText}`);\n                }\n                const data = await response.json();\n                if (!data.data || data.data.length === 0) {\n                    throw new Error('No embedding data returned from Jina API');\n                }\n                const embedding = data.data[0].embedding;\n                // Update usage stats\n                this.updateKeyUsage(apiKey, data.usage?.total_tokens || text.length);\n                console.log(`[Jina Embeddings] Success with key ${this.apiKeys.indexOf(apiKey) + 1} (${embedding.length} dimensions, ${data.usage?.total_tokens || 'unknown'} tokens)`);\n                return embedding;\n            } catch (error) {\n                lastError = error;\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1} failed:`, error.message);\n                // If this is the last attempt, throw the error\n                if (attempt === maxRetries - 1) {\n                    break;\n                }\n            }\n        }\n        // All keys failed\n        console.error(`[Jina Embeddings] All ${maxRetries} API keys failed`);\n        throw new Error(`All Jina API keys failed. Last error: ${lastError?.message || 'Unknown error'}`);\n    }\n    /**\r\n   * Generate embeddings for multiple texts (batch processing)\r\n   */ async embedDocuments(texts) {\n        // For now, process sequentially to avoid overwhelming the API\n        // TODO: Implement smart batching based on rate limits\n        const embeddings = [];\n        for(let i = 0; i < texts.length; i++){\n            console.log(`[Jina Embeddings] Processing document ${i + 1}/${texts.length}`);\n            const embedding = await this.embedQuery(texts[i]);\n            embeddings.push(embedding);\n            // Small delay to respect rate limits\n            if (i < texts.length - 1) {\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n        }\n        return embeddings;\n    }\n    /**\r\n   * Get usage statistics for all keys\r\n   */ getUsageStats() {\n        const stats = {};\n        this.apiKeys.forEach((key, index)=>{\n            const keyStats = this.keyUsage.get(key);\n            if (keyStats) {\n                stats[`key_${index + 1}`] = {\n                    ...keyStats\n                };\n            }\n        });\n        return stats;\n    }\n    /**\r\n   * Get total capacity across all keys\r\n   */ getTotalCapacity() {\n        return {\n            totalKeys: this.apiKeys.length,\n            estimatedRPM: this.apiKeys.length * 500,\n            estimatedTokensPerMonth: this.apiKeys.length * 1000000\n        };\n    }\n}\n// Export a singleton instance\nconst jinaEmbeddings = new MultiKeyJinaEmbeddings();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/embeddings/jina.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/semantic-cache/SemanticCacheService.ts":
/*!********************************************************!*\
  !*** ./src/lib/semantic-cache/SemanticCacheService.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SemanticCacheService: () => (/* binding */ SemanticCacheService),\n/* harmony export */   semanticCache: () => (/* binding */ semanticCache)\n/* harmony export */ });\n/* harmony import */ var _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/embeddings/jina */ \"(rsc)/./src/lib/embeddings/jina.ts\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Semantic Cache Service for RouKey\n * Provides advanced caching using vector embeddings for semantic similarity\n */ \n\n\n// Create service role client for server-side operations that need to bypass RLS\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\nclass SemanticCacheService {\n    constructor(){\n        // Cache configuration by tier\n        this.tierConfig = {\n            free: {\n                enabled: false,\n                ttlHours: 0,\n                similarityThreshold: 0.90,\n                maxCacheSize: 0\n            },\n            starter: {\n                enabled: false,\n                ttlHours: 0,\n                similarityThreshold: 0.90,\n                maxCacheSize: 0\n            },\n            pro: {\n                enabled: true,\n                ttlHours: 24,\n                similarityThreshold: 0.85,\n                maxCacheSize: 1000\n            },\n            enterprise: {\n                enabled: true,\n                ttlHours: 168,\n                similarityThreshold: 0.80,\n                maxCacheSize: 10000\n            }\n        };\n    }\n    static getInstance() {\n        if (!SemanticCacheService.instance) {\n            SemanticCacheService.instance = new SemanticCacheService();\n        }\n        return SemanticCacheService.instance;\n    }\n    /**\n   * Generate a hash for exact prompt matching\n   */ generatePromptHash(prompt, model, temperature) {\n        const hashInput = `${prompt}|${model}|${temperature || 0}`;\n        return crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash('sha256').update(hashInput).digest('hex');\n    }\n    /**\n   * Check if semantic caching is enabled for the given tier\n   */ isCacheEnabled(tier) {\n        return this.tierConfig[tier].enabled;\n    }\n    /**\n   * Get cache configuration for a tier\n   */ getTierConfig(tier) {\n        return this.tierConfig[tier];\n    }\n    /**\n   * Search for cached responses using semantic similarity\n   */ async searchCache(request, userId, configId, tier) {\n        try {\n            if (!this.isCacheEnabled(tier)) {\n                return null;\n            }\n            const config = this.getTierConfig(tier);\n            const supabase = createServiceRoleClient();\n            // First, try exact hash match for perfect duplicates\n            const promptHash = this.generatePromptHash(request.promptText, request.modelUsed, request.temperature);\n            const { data: exactMatch, error: exactError } = await supabase.from('semantic_cache').select('*').eq('prompt_hash', promptHash).eq('user_id', userId).eq('custom_api_config_id', configId).gt('expires_at', new Date().toISOString()).limit(1).single();\n            if (exactMatch && !exactError) {\n                console.log('[Semantic Cache] Exact hash match found');\n                await this.incrementHitCount(exactMatch.id);\n                return this.formatCachedResult(exactMatch, 1.0);\n            }\n            // If no exact match, try semantic similarity search\n            const queryEmbedding = await _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_0__.jinaEmbeddings.embedQuery(request.promptText);\n            const { data: semanticMatches, error: semanticError } = await supabase.rpc('search_semantic_cache', {\n                query_embedding: queryEmbedding,\n                config_id: configId,\n                user_id_param: userId,\n                similarity_threshold: config.similarityThreshold,\n                match_count: 1\n            });\n            if (semanticError) {\n                console.error('[Semantic Cache] Search error:', semanticError);\n                return null;\n            }\n            if (semanticMatches && semanticMatches.length > 0) {\n                const match = semanticMatches[0];\n                console.log(`[Semantic Cache] Semantic match found with ${(match.similarity * 100).toFixed(1)}% similarity`);\n                await this.incrementHitCount(match.id);\n                return this.formatCachedResult(match, match.similarity);\n            }\n            console.log('[Semantic Cache] No cache match found');\n            return null;\n        } catch (error) {\n            console.error('[Semantic Cache] Search error:', error);\n            return null;\n        }\n    }\n    /**\n   * Store a response in the semantic cache\n   */ async storeCache(request, response, userId, configId, tier) {\n        try {\n            if (!this.isCacheEnabled(tier)) {\n                return false;\n            }\n            const config = this.getTierConfig(tier);\n            const supabase = createServiceRoleClient();\n            // Generate embedding for the prompt\n            const promptEmbedding = await _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_0__.jinaEmbeddings.embedQuery(request.promptText);\n            const promptHash = this.generatePromptHash(request.promptText, request.modelUsed, request.temperature);\n            // Calculate expiration time\n            const expiresAt = new Date();\n            expiresAt.setHours(expiresAt.getHours() + config.ttlHours);\n            // Store in cache\n            const { error } = await supabase.from('semantic_cache').insert({\n                user_id: userId,\n                custom_api_config_id: configId,\n                prompt_text: request.promptText,\n                prompt_embedding: promptEmbedding,\n                prompt_hash: promptHash,\n                model_used: request.modelUsed,\n                provider_used: request.providerUsed,\n                temperature: request.temperature,\n                max_tokens: request.maxTokens,\n                request_metadata: request.metadata || {},\n                response_data: response.responseData,\n                response_tokens_prompt: response.tokensPrompt,\n                response_tokens_completion: response.tokensCompletion,\n                response_cost: response.cost,\n                cache_tier: tier,\n                expires_at: expiresAt.toISOString()\n            });\n            if (error) {\n                console.error('[Semantic Cache] Store error:', error);\n                return false;\n            }\n            console.log(`[Semantic Cache] Stored response for ${tier} tier (expires: ${expiresAt.toISOString()})`);\n            return true;\n        } catch (error) {\n            console.error('[Semantic Cache] Store error:', error);\n            return false;\n        }\n    }\n    /**\n   * Get cache statistics for a user/config\n   */ async getCacheStats(userId, configId, days = 7) {\n        try {\n            const supabase = createServiceRoleClient();\n            const startDate = new Date();\n            startDate.setDate(startDate.getDate() - days);\n            const { data, error } = await supabase.from('semantic_cache_analytics').select('*').eq('user_id', userId).eq('custom_api_config_id', configId).gte('date', startDate.toISOString().split('T')[0]);\n            if (error) {\n                console.error('[Semantic Cache] Stats error:', error);\n                return this.getEmptyStats();\n            }\n            if (!data || data.length === 0) {\n                return this.getEmptyStats();\n            }\n            // Aggregate statistics\n            const totals = data.reduce((acc, row)=>({\n                    totalRequests: acc.totalRequests + (row.total_requests || 0),\n                    cacheHits: acc.cacheHits + (row.cache_hits || 0),\n                    cacheMisses: acc.cacheMisses + (row.cache_misses || 0),\n                    tokensSaved: acc.tokensSaved + (row.tokens_saved || 0),\n                    costSaved: acc.costSaved + (row.cost_saved || 0),\n                    responseTimeSum: acc.responseTimeSum + (row.avg_response_time_ms || 0),\n                    rowCount: acc.rowCount + 1\n                }), {\n                totalRequests: 0,\n                cacheHits: 0,\n                cacheMisses: 0,\n                tokensSaved: 0,\n                costSaved: 0,\n                responseTimeSum: 0,\n                rowCount: 0\n            });\n            return {\n                totalRequests: totals.totalRequests,\n                cacheHits: totals.cacheHits,\n                cacheMisses: totals.cacheMisses,\n                hitRate: totals.totalRequests > 0 ? totals.cacheHits / totals.totalRequests : 0,\n                tokensSaved: totals.tokensSaved,\n                costSaved: totals.costSaved,\n                avgResponseTime: totals.rowCount > 0 ? totals.responseTimeSum / totals.rowCount : 0\n            };\n        } catch (error) {\n            console.error('[Semantic Cache] Stats error:', error);\n            return this.getEmptyStats();\n        }\n    }\n    /**\n   * Clean up expired cache entries\n   */ async cleanupExpiredCache() {\n        try {\n            const supabase = createServiceRoleClient();\n            const { data, error } = await supabase.rpc('cleanup_semantic_cache');\n            if (error) {\n                console.error('[Semantic Cache] Cleanup error:', error);\n                return 0;\n            }\n            console.log(`[Semantic Cache] Cleaned up ${data} expired entries`);\n            return data || 0;\n        } catch (error) {\n            console.error('[Semantic Cache] Cleanup error:', error);\n            return 0;\n        }\n    }\n    /**\n   * Private helper methods\n   */ async incrementHitCount(cacheId) {\n        try {\n            const supabase = createServiceRoleClient();\n            await supabase.rpc('increment_cache_hit', {\n                cache_id: cacheId\n            });\n        } catch (error) {\n            console.error('[Semantic Cache] Hit count increment error:', error);\n        }\n    }\n    formatCachedResult(cacheEntry, similarity) {\n        return {\n            id: cacheEntry.id,\n            promptText: cacheEntry.prompt_text,\n            responseData: cacheEntry.response_data,\n            modelUsed: cacheEntry.model_used,\n            providerUsed: cacheEntry.provider_used,\n            similarity,\n            hitCount: cacheEntry.hit_count,\n            createdAt: cacheEntry.created_at,\n            expiresAt: cacheEntry.expires_at\n        };\n    }\n    getEmptyStats() {\n        return {\n            totalRequests: 0,\n            cacheHits: 0,\n            cacheMisses: 0,\n            hitRate: 0,\n            tokensSaved: 0,\n            costSaved: 0,\n            avgResponseTime: 0\n        };\n    }\n}\n// Export singleton instance\nconst semanticCache = SemanticCacheService.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlbWFudGljLWNhY2hlL1NlbWFudGljQ2FjaGVTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBOzs7Q0FHQyxHQUVzRDtBQUNGO0FBQ3pCO0FBRTVCLGdGQUFnRjtBQUNoRixTQUFTRztJQUNQLE9BQU9GLHNHQUFZQSxDQUNqQkcsMENBQW9DLEVBQ3BDQSxRQUFRQyxHQUFHLENBQUNFLHlCQUF5QixFQUNyQztRQUNFQyxNQUFNO1lBQ0pDLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7QUFFSjtBQTBDTyxNQUFNQztJQStCWCxhQUFzQjtRQTVCdEIsOEJBQThCO2FBQ2JDLGFBQWE7WUFDNUJDLE1BQU07Z0JBQ0pDLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLHFCQUFxQjtnQkFDckJDLGNBQWM7WUFDaEI7WUFDQUMsU0FBUztnQkFDUEosU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMscUJBQXFCO2dCQUNyQkMsY0FBYztZQUNoQjtZQUNBRSxLQUFLO2dCQUNITCxTQUFTO2dCQUNUQyxVQUFVO2dCQUNWQyxxQkFBcUI7Z0JBQ3JCQyxjQUFjO1lBQ2hCO1lBQ0FHLFlBQVk7Z0JBQ1ZOLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLHFCQUFxQjtnQkFDckJDLGNBQWM7WUFDaEI7UUFDRjtJQUV1QjtJQUV2QixPQUFjSSxjQUFvQztRQUNoRCxJQUFJLENBQUNWLHFCQUFxQlcsUUFBUSxFQUFFO1lBQ2xDWCxxQkFBcUJXLFFBQVEsR0FBRyxJQUFJWDtRQUN0QztRQUNBLE9BQU9BLHFCQUFxQlcsUUFBUTtJQUN0QztJQUVBOztHQUVDLEdBQ0QsbUJBQTJCRSxNQUFjLEVBQUVDLEtBQWEsRUFBRUMsV0FBb0IsRUFBVTtRQUN0RixNQUFNQyxZQUFZLEdBQUdILE9BQU8sQ0FBQyxFQUFFQyxNQUFNLENBQUMsRUFBRUMsZUFBZSxHQUFHO1FBQzFELE9BQU94Qix3REFBaUIsQ0FBQyxVQUFVMkIsTUFBTSxDQUFDRixXQUFXRyxNQUFNLENBQUM7SUFDOUQ7SUFFQTs7R0FFQyxHQUNELGVBQXVCRSxJQUFlLEVBQVc7UUFDL0MsT0FBTyxJQUFJLENBQUNwQixVQUFVLENBQUNvQixLQUFLLENBQUNsQixPQUFPO0lBQ3RDO0lBRUE7O0dBRUMsR0FDRCxjQUFzQmtCLElBQWUsRUFBRTtRQUNyQyxPQUFPLElBQUksQ0FBQ3BCLFVBQVUsQ0FBQ29CLEtBQUs7SUFDOUI7SUFFQTs7R0FFQyxHQUNELE1BQU1FLFlBQ0pDLE9BQXFCLEVBQ3JCQyxNQUFjLEVBQ2RDLFFBQWdCLEVBQ2hCTCxJQUFlLEVBQ2U7UUFDOUIsSUFBSTtZQUNGLElBQUksQ0FBQyxJQUFJLENBQUNELGNBQWMsQ0FBQ0MsT0FBTztnQkFDOUIsT0FBTztZQUNUO1lBRUEsTUFBTU0sU0FBUyxJQUFJLENBQUNMLGFBQWEsQ0FBQ0Q7WUFDbEMsTUFBTU8sV0FBV3BDO1lBRWpCLHFEQUFxRDtZQUNyRCxNQUFNcUMsYUFBYSxJQUFJLENBQUNqQixrQkFBa0IsQ0FDeENZLFFBQVFNLFVBQVUsRUFDbEJOLFFBQVFPLFNBQVMsRUFDakJQLFFBQVFULFdBQVc7WUFHckIsTUFBTSxFQUFFaUIsTUFBTUMsVUFBVSxFQUFFQyxPQUFPQyxVQUFVLEVBQUUsR0FBRyxNQUFNUCxTQUNuRFEsSUFBSSxDQUFDLGtCQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGVBQWVULFlBQ2xCUyxFQUFFLENBQUMsV0FBV2IsUUFDZGEsRUFBRSxDQUFDLHdCQUF3QlosVUFDM0JhLEVBQUUsQ0FBQyxjQUFjLElBQUlDLE9BQU9DLFdBQVcsSUFDdkNDLEtBQUssQ0FBQyxHQUNOQyxNQUFNO1lBRVQsSUFBSVYsY0FBYyxDQUFDRSxZQUFZO2dCQUM3QlMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE1BQU0sSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ2IsV0FBV2MsRUFBRTtnQkFDMUMsT0FBTyxJQUFJLENBQUNDLGtCQUFrQixDQUFDZixZQUFZO1lBQzdDO1lBRUEsb0RBQW9EO1lBQ3BELE1BQU1nQixpQkFBaUIsTUFBTTVELGdFQUFjQSxDQUFDNkQsVUFBVSxDQUFDMUIsUUFBUU0sVUFBVTtZQUV6RSxNQUFNLEVBQUVFLE1BQU1tQixlQUFlLEVBQUVqQixPQUFPa0IsYUFBYSxFQUFFLEdBQUcsTUFBTXhCLFNBQzNEeUIsR0FBRyxDQUFDLHlCQUF5QjtnQkFDNUJDLGlCQUFpQkw7Z0JBQ2pCTSxXQUFXN0I7Z0JBQ1g4QixlQUFlL0I7Z0JBQ2ZnQyxzQkFBc0I5QixPQUFPdEIsbUJBQW1CO2dCQUNoRHFELGFBQWE7WUFDZjtZQUVGLElBQUlOLGVBQWU7Z0JBQ2pCUixRQUFRVixLQUFLLENBQUMsa0NBQWtDa0I7Z0JBQ2hELE9BQU87WUFDVDtZQUVBLElBQUlELG1CQUFtQkEsZ0JBQWdCUSxNQUFNLEdBQUcsR0FBRztnQkFDakQsTUFBTUMsUUFBUVQsZUFBZSxDQUFDLEVBQUU7Z0JBQ2hDUCxRQUFRQyxHQUFHLENBQUMsQ0FBQywyQ0FBMkMsRUFBRSxDQUFDZSxNQUFNQyxVQUFVLEdBQUcsR0FBRSxFQUFHQyxPQUFPLENBQUMsR0FBRyxZQUFZLENBQUM7Z0JBQzNHLE1BQU0sSUFBSSxDQUFDaEIsaUJBQWlCLENBQUNjLE1BQU1iLEVBQUU7Z0JBQ3JDLE9BQU8sSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQ1ksT0FBT0EsTUFBTUMsVUFBVTtZQUN4RDtZQUVBakIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUVULEVBQUUsT0FBT1gsT0FBTztZQUNkVSxRQUFRVixLQUFLLENBQUMsa0NBQWtDQTtZQUNoRCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTTZCLFdBQ0p2QyxPQUFxQixFQUNyQndDLFFBQXVCLEVBQ3ZCdkMsTUFBYyxFQUNkQyxRQUFnQixFQUNoQkwsSUFBZSxFQUNHO1FBQ2xCLElBQUk7WUFDRixJQUFJLENBQUMsSUFBSSxDQUFDRCxjQUFjLENBQUNDLE9BQU87Z0JBQzlCLE9BQU87WUFDVDtZQUVBLE1BQU1NLFNBQVMsSUFBSSxDQUFDTCxhQUFhLENBQUNEO1lBQ2xDLE1BQU1PLFdBQVdwQztZQUVqQixvQ0FBb0M7WUFDcEMsTUFBTXlFLGtCQUFrQixNQUFNNUUsZ0VBQWNBLENBQUM2RCxVQUFVLENBQUMxQixRQUFRTSxVQUFVO1lBQzFFLE1BQU1ELGFBQWEsSUFBSSxDQUFDakIsa0JBQWtCLENBQ3hDWSxRQUFRTSxVQUFVLEVBQ2xCTixRQUFRTyxTQUFTLEVBQ2pCUCxRQUFRVCxXQUFXO1lBR3JCLDRCQUE0QjtZQUM1QixNQUFNbUQsWUFBWSxJQUFJMUI7WUFDdEIwQixVQUFVQyxRQUFRLENBQUNELFVBQVVFLFFBQVEsS0FBS3pDLE9BQU92QixRQUFRO1lBRXpELGlCQUFpQjtZQUNqQixNQUFNLEVBQUU4QixLQUFLLEVBQUUsR0FBRyxNQUFNTixTQUNyQlEsSUFBSSxDQUFDLGtCQUNMaUMsTUFBTSxDQUFDO2dCQUNOQyxTQUFTN0M7Z0JBQ1Q4QyxzQkFBc0I3QztnQkFDdEI4QyxhQUFhaEQsUUFBUU0sVUFBVTtnQkFDL0IyQyxrQkFBa0JSO2dCQUNsQlMsYUFBYTdDO2dCQUNiOEMsWUFBWW5ELFFBQVFPLFNBQVM7Z0JBQzdCNkMsZUFBZXBELFFBQVFxRCxZQUFZO2dCQUNuQzlELGFBQWFTLFFBQVFULFdBQVc7Z0JBQ2hDK0QsWUFBWXRELFFBQVF1RCxTQUFTO2dCQUM3QkMsa0JBQWtCeEQsUUFBUXlELFFBQVEsSUFBSSxDQUFDO2dCQUN2Q0MsZUFBZWxCLFNBQVNtQixZQUFZO2dCQUNwQ0Msd0JBQXdCcEIsU0FBU3FCLFlBQVk7Z0JBQzdDQyw0QkFBNEJ0QixTQUFTdUIsZ0JBQWdCO2dCQUNyREMsZUFBZXhCLFNBQVN5QixJQUFJO2dCQUM1QkMsWUFBWXJFO2dCQUNac0UsWUFBWXpCLFVBQVV6QixXQUFXO1lBQ25DO1lBRUYsSUFBSVAsT0FBTztnQkFDVFUsUUFBUVYsS0FBSyxDQUFDLGlDQUFpQ0E7Z0JBQy9DLE9BQU87WUFDVDtZQUVBVSxRQUFRQyxHQUFHLENBQUMsQ0FBQyxxQ0FBcUMsRUFBRXhCLEtBQUssZ0JBQWdCLEVBQUU2QyxVQUFVekIsV0FBVyxHQUFHLENBQUMsQ0FBQztZQUNyRyxPQUFPO1FBRVQsRUFBRSxPQUFPUCxPQUFPO1lBQ2RVLFFBQVFWLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DLE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNMEQsY0FDSm5FLE1BQWMsRUFDZEMsUUFBZ0IsRUFDaEJtRSxPQUFlLENBQUMsRUFDSztRQUNyQixJQUFJO1lBQ0YsTUFBTWpFLFdBQVdwQztZQUNqQixNQUFNc0csWUFBWSxJQUFJdEQ7WUFDdEJzRCxVQUFVQyxPQUFPLENBQUNELFVBQVVFLE9BQU8sS0FBS0g7WUFFeEMsTUFBTSxFQUFFN0QsSUFBSSxFQUFFRSxLQUFLLEVBQUUsR0FBRyxNQUFNTixTQUMzQlEsSUFBSSxDQUFDLDRCQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFdBQVdiLFFBQ2RhLEVBQUUsQ0FBQyx3QkFBd0JaLFVBQzNCdUUsR0FBRyxDQUFDLFFBQVFILFVBQVVyRCxXQUFXLEdBQUd5RCxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFFcEQsSUFBSWhFLE9BQU87Z0JBQ1RVLFFBQVFWLEtBQUssQ0FBQyxpQ0FBaUNBO2dCQUMvQyxPQUFPLElBQUksQ0FBQ2lFLGFBQWE7WUFDM0I7WUFFQSxJQUFJLENBQUNuRSxRQUFRQSxLQUFLMkIsTUFBTSxLQUFLLEdBQUc7Z0JBQzlCLE9BQU8sSUFBSSxDQUFDd0MsYUFBYTtZQUMzQjtZQUVBLHVCQUF1QjtZQUN2QixNQUFNQyxTQUFTcEUsS0FBS3FFLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxNQUFTO29CQUN4Q0MsZUFBZUYsSUFBSUUsYUFBYSxHQUFJRCxDQUFBQSxJQUFJRSxjQUFjLElBQUk7b0JBQzFEQyxXQUFXSixJQUFJSSxTQUFTLEdBQUlILENBQUFBLElBQUlJLFVBQVUsSUFBSTtvQkFDOUNDLGFBQWFOLElBQUlNLFdBQVcsR0FBSUwsQ0FBQUEsSUFBSU0sWUFBWSxJQUFJO29CQUNwREMsYUFBYVIsSUFBSVEsV0FBVyxHQUFJUCxDQUFBQSxJQUFJUSxZQUFZLElBQUk7b0JBQ3BEQyxXQUFXVixJQUFJVSxTQUFTLEdBQUlULENBQUFBLElBQUlVLFVBQVUsSUFBSTtvQkFDOUNDLGlCQUFpQlosSUFBSVksZUFBZSxHQUFJWCxDQUFBQSxJQUFJWSxvQkFBb0IsSUFBSTtvQkFDcEVDLFVBQVVkLElBQUljLFFBQVEsR0FBRztnQkFDM0IsSUFBSTtnQkFDRlosZUFBZTtnQkFDZkUsV0FBVztnQkFDWEUsYUFBYTtnQkFDYkUsYUFBYTtnQkFDYkUsV0FBVztnQkFDWEUsaUJBQWlCO2dCQUNqQkUsVUFBVTtZQUNaO1lBRUEsT0FBTztnQkFDTFosZUFBZUosT0FBT0ksYUFBYTtnQkFDbkNFLFdBQVdOLE9BQU9NLFNBQVM7Z0JBQzNCRSxhQUFhUixPQUFPUSxXQUFXO2dCQUMvQlMsU0FBU2pCLE9BQU9JLGFBQWEsR0FBRyxJQUFJSixPQUFPTSxTQUFTLEdBQUdOLE9BQU9JLGFBQWEsR0FBRztnQkFDOUVNLGFBQWFWLE9BQU9VLFdBQVc7Z0JBQy9CRSxXQUFXWixPQUFPWSxTQUFTO2dCQUMzQk0saUJBQWlCbEIsT0FBT2dCLFFBQVEsR0FBRyxJQUFJaEIsT0FBT2MsZUFBZSxHQUFHZCxPQUFPZ0IsUUFBUSxHQUFHO1lBQ3BGO1FBRUYsRUFBRSxPQUFPbEYsT0FBTztZQUNkVSxRQUFRVixLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPLElBQUksQ0FBQ2lFLGFBQWE7UUFDM0I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTW9CLHNCQUF1QztRQUMzQyxJQUFJO1lBQ0YsTUFBTTNGLFdBQVdwQztZQUVqQixNQUFNLEVBQUV3QyxJQUFJLEVBQUVFLEtBQUssRUFBRSxHQUFHLE1BQU1OLFNBQzNCeUIsR0FBRyxDQUFDO1lBRVAsSUFBSW5CLE9BQU87Z0JBQ1RVLFFBQVFWLEtBQUssQ0FBQyxtQ0FBbUNBO2dCQUNqRCxPQUFPO1lBQ1Q7WUFFQVUsUUFBUUMsR0FBRyxDQUFDLENBQUMsNEJBQTRCLEVBQUViLEtBQUssZ0JBQWdCLENBQUM7WUFDakUsT0FBT0EsUUFBUTtRQUVqQixFQUFFLE9BQU9FLE9BQU87WUFDZFUsUUFBUVYsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakQsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWNZLGtCQUFrQjBFLE9BQWUsRUFBaUI7UUFDOUQsSUFBSTtZQUNGLE1BQU01RixXQUFXcEM7WUFDakIsTUFBTW9DLFNBQVN5QixHQUFHLENBQUMsdUJBQXVCO2dCQUFFb0UsVUFBVUQ7WUFBUTtRQUNoRSxFQUFFLE9BQU90RixPQUFPO1lBQ2RVLFFBQVFWLEtBQUssQ0FBQywrQ0FBK0NBO1FBQy9EO0lBQ0Y7SUFFUWMsbUJBQW1CMEUsVUFBZSxFQUFFN0QsVUFBa0IsRUFBZ0I7UUFDNUUsT0FBTztZQUNMZCxJQUFJMkUsV0FBVzNFLEVBQUU7WUFDakJqQixZQUFZNEYsV0FBV2xELFdBQVc7WUFDbENXLGNBQWN1QyxXQUFXeEMsYUFBYTtZQUN0Q25ELFdBQVcyRixXQUFXL0MsVUFBVTtZQUNoQ0UsY0FBYzZDLFdBQVc5QyxhQUFhO1lBQ3RDZjtZQUNBOEQsVUFBVUQsV0FBV0UsU0FBUztZQUM5QkMsV0FBV0gsV0FBV0ksVUFBVTtZQUNoQzVELFdBQVd3RCxXQUFXL0IsVUFBVTtRQUNsQztJQUNGO0lBRVFRLGdCQUE0QjtRQUNsQyxPQUFPO1lBQ0xLLGVBQWU7WUFDZkUsV0FBVztZQUNYRSxhQUFhO1lBQ2JTLFNBQVM7WUFDVFAsYUFBYTtZQUNiRSxXQUFXO1lBQ1hNLGlCQUFpQjtRQUNuQjtJQUNGO0FBQ0Y7QUFFQSw0QkFBNEI7QUFDckIsTUFBTVMsZ0JBQWdCL0gscUJBQXFCVSxXQUFXLEdBQUciLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxsaWJcXHNlbWFudGljLWNhY2hlXFxTZW1hbnRpY0NhY2hlU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFNlbWFudGljIENhY2hlIFNlcnZpY2UgZm9yIFJvdUtleVxuICogUHJvdmlkZXMgYWR2YW5jZWQgY2FjaGluZyB1c2luZyB2ZWN0b3IgZW1iZWRkaW5ncyBmb3Igc2VtYW50aWMgc2ltaWxhcml0eVxuICovXG5cbmltcG9ydCB7IGppbmFFbWJlZGRpbmdzIH0gZnJvbSAnQC9saWIvZW1iZWRkaW5ncy9qaW5hJztcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcyc7XG5pbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5cbi8vIENyZWF0ZSBzZXJ2aWNlIHJvbGUgY2xpZW50IGZvciBzZXJ2ZXItc2lkZSBvcGVyYXRpb25zIHRoYXQgbmVlZCB0byBieXBhc3MgUkxTXG5mdW5jdGlvbiBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhLFxuICAgIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDYWNoZVJlcXVlc3Qge1xuICBwcm9tcHRUZXh0OiBzdHJpbmc7XG4gIG1vZGVsVXNlZDogc3RyaW5nO1xuICBwcm92aWRlclVzZWQ6IHN0cmluZztcbiAgdGVtcGVyYXR1cmU/OiBudW1iZXI7XG4gIG1heFRva2Vucz86IG51bWJlcjtcbiAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENhY2hlUmVzcG9uc2Uge1xuICByZXNwb25zZURhdGE6IGFueTtcbiAgdG9rZW5zUHJvbXB0PzogbnVtYmVyO1xuICB0b2tlbnNDb21wbGV0aW9uPzogbnVtYmVyO1xuICBjb3N0PzogbnVtYmVyO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENhY2hlZFJlc3VsdCB7XG4gIGlkOiBzdHJpbmc7XG4gIHByb21wdFRleHQ6IHN0cmluZztcbiAgcmVzcG9uc2VEYXRhOiBhbnk7XG4gIG1vZGVsVXNlZDogc3RyaW5nO1xuICBwcm92aWRlclVzZWQ6IHN0cmluZztcbiAgc2ltaWxhcml0eTogbnVtYmVyO1xuICBoaXRDb3VudDogbnVtYmVyO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgZXhwaXJlc0F0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ2FjaGVTdGF0cyB7XG4gIHRvdGFsUmVxdWVzdHM6IG51bWJlcjtcbiAgY2FjaGVIaXRzOiBudW1iZXI7XG4gIGNhY2hlTWlzc2VzOiBudW1iZXI7XG4gIGhpdFJhdGU6IG51bWJlcjtcbiAgdG9rZW5zU2F2ZWQ6IG51bWJlcjtcbiAgY29zdFNhdmVkOiBudW1iZXI7XG4gIGF2Z1Jlc3BvbnNlVGltZTogbnVtYmVyO1xufVxuXG5leHBvcnQgdHlwZSBDYWNoZVRpZXIgPSAnZnJlZScgfCAnc3RhcnRlcicgfCAncHJvJyB8ICdlbnRlcnByaXNlJztcblxuZXhwb3J0IGNsYXNzIFNlbWFudGljQ2FjaGVTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IFNlbWFudGljQ2FjaGVTZXJ2aWNlO1xuICBcbiAgLy8gQ2FjaGUgY29uZmlndXJhdGlvbiBieSB0aWVyXG4gIHByaXZhdGUgcmVhZG9ubHkgdGllckNvbmZpZyA9IHtcbiAgICBmcmVlOiB7XG4gICAgICBlbmFibGVkOiBmYWxzZSxcbiAgICAgIHR0bEhvdXJzOiAwLFxuICAgICAgc2ltaWxhcml0eVRocmVzaG9sZDogMC45MCxcbiAgICAgIG1heENhY2hlU2l6ZTogMFxuICAgIH0sXG4gICAgc3RhcnRlcjoge1xuICAgICAgZW5hYmxlZDogZmFsc2UsIC8vIFNpbXBsZSBjYWNoaW5nIG9ubHkgZm9yIHN0YXJ0ZXJcbiAgICAgIHR0bEhvdXJzOiAwLFxuICAgICAgc2ltaWxhcml0eVRocmVzaG9sZDogMC45MCxcbiAgICAgIG1heENhY2hlU2l6ZTogMFxuICAgIH0sXG4gICAgcHJvOiB7XG4gICAgICBlbmFibGVkOiB0cnVlLFxuICAgICAgdHRsSG91cnM6IDI0LCAvLyAxIGRheVxuICAgICAgc2ltaWxhcml0eVRocmVzaG9sZDogMC44NSxcbiAgICAgIG1heENhY2hlU2l6ZTogMTAwMFxuICAgIH0sXG4gICAgZW50ZXJwcmlzZToge1xuICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgIHR0bEhvdXJzOiAxNjgsIC8vIDEgd2Vla1xuICAgICAgc2ltaWxhcml0eVRocmVzaG9sZDogMC44MCxcbiAgICAgIG1heENhY2hlU2l6ZTogMTAwMDBcbiAgICB9XG4gIH07XG5cbiAgcHJpdmF0ZSBjb25zdHJ1Y3RvcigpIHt9XG5cbiAgcHVibGljIHN0YXRpYyBnZXRJbnN0YW5jZSgpOiBTZW1hbnRpY0NhY2hlU2VydmljZSB7XG4gICAgaWYgKCFTZW1hbnRpY0NhY2hlU2VydmljZS5pbnN0YW5jZSkge1xuICAgICAgU2VtYW50aWNDYWNoZVNlcnZpY2UuaW5zdGFuY2UgPSBuZXcgU2VtYW50aWNDYWNoZVNlcnZpY2UoKTtcbiAgICB9XG4gICAgcmV0dXJuIFNlbWFudGljQ2FjaGVTZXJ2aWNlLmluc3RhbmNlO1xuICB9XG5cbiAgLyoqXG4gICAqIEdlbmVyYXRlIGEgaGFzaCBmb3IgZXhhY3QgcHJvbXB0IG1hdGNoaW5nXG4gICAqL1xuICBwcml2YXRlIGdlbmVyYXRlUHJvbXB0SGFzaChwcm9tcHQ6IHN0cmluZywgbW9kZWw6IHN0cmluZywgdGVtcGVyYXR1cmU/OiBudW1iZXIpOiBzdHJpbmcge1xuICAgIGNvbnN0IGhhc2hJbnB1dCA9IGAke3Byb21wdH18JHttb2RlbH18JHt0ZW1wZXJhdHVyZSB8fCAwfWA7XG4gICAgcmV0dXJuIGNyeXB0by5jcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoaGFzaElucHV0KS5kaWdlc3QoJ2hleCcpO1xuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIHNlbWFudGljIGNhY2hpbmcgaXMgZW5hYmxlZCBmb3IgdGhlIGdpdmVuIHRpZXJcbiAgICovXG4gIHByaXZhdGUgaXNDYWNoZUVuYWJsZWQodGllcjogQ2FjaGVUaWVyKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMudGllckNvbmZpZ1t0aWVyXS5lbmFibGVkO1xuICB9XG5cbiAgLyoqXG4gICAqIEdldCBjYWNoZSBjb25maWd1cmF0aW9uIGZvciBhIHRpZXJcbiAgICovXG4gIHByaXZhdGUgZ2V0VGllckNvbmZpZyh0aWVyOiBDYWNoZVRpZXIpIHtcbiAgICByZXR1cm4gdGhpcy50aWVyQ29uZmlnW3RpZXJdO1xuICB9XG5cbiAgLyoqXG4gICAqIFNlYXJjaCBmb3IgY2FjaGVkIHJlc3BvbnNlcyB1c2luZyBzZW1hbnRpYyBzaW1pbGFyaXR5XG4gICAqL1xuICBhc3luYyBzZWFyY2hDYWNoZShcbiAgICByZXF1ZXN0OiBDYWNoZVJlcXVlc3QsXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgY29uZmlnSWQ6IHN0cmluZyxcbiAgICB0aWVyOiBDYWNoZVRpZXJcbiAgKTogUHJvbWlzZTxDYWNoZWRSZXN1bHQgfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdGhpcy5pc0NhY2hlRW5hYmxlZCh0aWVyKSkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY29uZmlnID0gdGhpcy5nZXRUaWVyQ29uZmlnKHRpZXIpO1xuICAgICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpO1xuXG4gICAgICAvLyBGaXJzdCwgdHJ5IGV4YWN0IGhhc2ggbWF0Y2ggZm9yIHBlcmZlY3QgZHVwbGljYXRlc1xuICAgICAgY29uc3QgcHJvbXB0SGFzaCA9IHRoaXMuZ2VuZXJhdGVQcm9tcHRIYXNoKFxuICAgICAgICByZXF1ZXN0LnByb21wdFRleHQsXG4gICAgICAgIHJlcXVlc3QubW9kZWxVc2VkLFxuICAgICAgICByZXF1ZXN0LnRlbXBlcmF0dXJlXG4gICAgICApO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IGV4YWN0TWF0Y2gsIGVycm9yOiBleGFjdEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnc2VtYW50aWNfY2FjaGUnKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCdwcm9tcHRfaGFzaCcsIHByb21wdEhhc2gpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgICAgLmVxKCdjdXN0b21fYXBpX2NvbmZpZ19pZCcsIGNvbmZpZ0lkKVxuICAgICAgICAuZ3QoJ2V4cGlyZXNfYXQnLCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkpXG4gICAgICAgIC5saW1pdCgxKVxuICAgICAgICAuc2luZ2xlKCk7XG5cbiAgICAgIGlmIChleGFjdE1hdGNoICYmICFleGFjdEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbU2VtYW50aWMgQ2FjaGVdIEV4YWN0IGhhc2ggbWF0Y2ggZm91bmQnKTtcbiAgICAgICAgYXdhaXQgdGhpcy5pbmNyZW1lbnRIaXRDb3VudChleGFjdE1hdGNoLmlkKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybWF0Q2FjaGVkUmVzdWx0KGV4YWN0TWF0Y2gsIDEuMCk7XG4gICAgICB9XG5cbiAgICAgIC8vIElmIG5vIGV4YWN0IG1hdGNoLCB0cnkgc2VtYW50aWMgc2ltaWxhcml0eSBzZWFyY2hcbiAgICAgIGNvbnN0IHF1ZXJ5RW1iZWRkaW5nID0gYXdhaXQgamluYUVtYmVkZGluZ3MuZW1iZWRRdWVyeShyZXF1ZXN0LnByb21wdFRleHQpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHNlbWFudGljTWF0Y2hlcywgZXJyb3I6IHNlbWFudGljRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5ycGMoJ3NlYXJjaF9zZW1hbnRpY19jYWNoZScsIHtcbiAgICAgICAgICBxdWVyeV9lbWJlZGRpbmc6IHF1ZXJ5RW1iZWRkaW5nLFxuICAgICAgICAgIGNvbmZpZ19pZDogY29uZmlnSWQsXG4gICAgICAgICAgdXNlcl9pZF9wYXJhbTogdXNlcklkLFxuICAgICAgICAgIHNpbWlsYXJpdHlfdGhyZXNob2xkOiBjb25maWcuc2ltaWxhcml0eVRocmVzaG9sZCxcbiAgICAgICAgICBtYXRjaF9jb3VudDogMVxuICAgICAgICB9KTtcblxuICAgICAgaWYgKHNlbWFudGljRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignW1NlbWFudGljIENhY2hlXSBTZWFyY2ggZXJyb3I6Jywgc2VtYW50aWNFcnJvcik7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuXG4gICAgICBpZiAoc2VtYW50aWNNYXRjaGVzICYmIHNlbWFudGljTWF0Y2hlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoID0gc2VtYW50aWNNYXRjaGVzWzBdO1xuICAgICAgICBjb25zb2xlLmxvZyhgW1NlbWFudGljIENhY2hlXSBTZW1hbnRpYyBtYXRjaCBmb3VuZCB3aXRoICR7KG1hdGNoLnNpbWlsYXJpdHkgKiAxMDApLnRvRml4ZWQoMSl9JSBzaW1pbGFyaXR5YCk7XG4gICAgICAgIGF3YWl0IHRoaXMuaW5jcmVtZW50SGl0Q291bnQobWF0Y2guaWQpO1xuICAgICAgICByZXR1cm4gdGhpcy5mb3JtYXRDYWNoZWRSZXN1bHQobWF0Y2gsIG1hdGNoLnNpbWlsYXJpdHkpO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnW1NlbWFudGljIENhY2hlXSBObyBjYWNoZSBtYXRjaCBmb3VuZCcpO1xuICAgICAgcmV0dXJuIG51bGw7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW1NlbWFudGljIENhY2hlXSBTZWFyY2ggZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFN0b3JlIGEgcmVzcG9uc2UgaW4gdGhlIHNlbWFudGljIGNhY2hlXG4gICAqL1xuICBhc3luYyBzdG9yZUNhY2hlKFxuICAgIHJlcXVlc3Q6IENhY2hlUmVxdWVzdCxcbiAgICByZXNwb25zZTogQ2FjaGVSZXNwb25zZSxcbiAgICB1c2VySWQ6IHN0cmluZyxcbiAgICBjb25maWdJZDogc3RyaW5nLFxuICAgIHRpZXI6IENhY2hlVGllclxuICApOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCF0aGlzLmlzQ2FjaGVFbmFibGVkKHRpZXIpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY29uZmlnID0gdGhpcy5nZXRUaWVyQ29uZmlnKHRpZXIpO1xuICAgICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpO1xuXG4gICAgICAvLyBHZW5lcmF0ZSBlbWJlZGRpbmcgZm9yIHRoZSBwcm9tcHRcbiAgICAgIGNvbnN0IHByb21wdEVtYmVkZGluZyA9IGF3YWl0IGppbmFFbWJlZGRpbmdzLmVtYmVkUXVlcnkocmVxdWVzdC5wcm9tcHRUZXh0KTtcbiAgICAgIGNvbnN0IHByb21wdEhhc2ggPSB0aGlzLmdlbmVyYXRlUHJvbXB0SGFzaChcbiAgICAgICAgcmVxdWVzdC5wcm9tcHRUZXh0LFxuICAgICAgICByZXF1ZXN0Lm1vZGVsVXNlZCxcbiAgICAgICAgcmVxdWVzdC50ZW1wZXJhdHVyZVxuICAgICAgKTtcblxuICAgICAgLy8gQ2FsY3VsYXRlIGV4cGlyYXRpb24gdGltZVxuICAgICAgY29uc3QgZXhwaXJlc0F0ID0gbmV3IERhdGUoKTtcbiAgICAgIGV4cGlyZXNBdC5zZXRIb3VycyhleHBpcmVzQXQuZ2V0SG91cnMoKSArIGNvbmZpZy50dGxIb3Vycyk7XG5cbiAgICAgIC8vIFN0b3JlIGluIGNhY2hlXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnc2VtYW50aWNfY2FjaGUnKVxuICAgICAgICAuaW5zZXJ0KHtcbiAgICAgICAgICB1c2VyX2lkOiB1c2VySWQsXG4gICAgICAgICAgY3VzdG9tX2FwaV9jb25maWdfaWQ6IGNvbmZpZ0lkLFxuICAgICAgICAgIHByb21wdF90ZXh0OiByZXF1ZXN0LnByb21wdFRleHQsXG4gICAgICAgICAgcHJvbXB0X2VtYmVkZGluZzogcHJvbXB0RW1iZWRkaW5nLFxuICAgICAgICAgIHByb21wdF9oYXNoOiBwcm9tcHRIYXNoLFxuICAgICAgICAgIG1vZGVsX3VzZWQ6IHJlcXVlc3QubW9kZWxVc2VkLFxuICAgICAgICAgIHByb3ZpZGVyX3VzZWQ6IHJlcXVlc3QucHJvdmlkZXJVc2VkLFxuICAgICAgICAgIHRlbXBlcmF0dXJlOiByZXF1ZXN0LnRlbXBlcmF0dXJlLFxuICAgICAgICAgIG1heF90b2tlbnM6IHJlcXVlc3QubWF4VG9rZW5zLFxuICAgICAgICAgIHJlcXVlc3RfbWV0YWRhdGE6IHJlcXVlc3QubWV0YWRhdGEgfHwge30sXG4gICAgICAgICAgcmVzcG9uc2VfZGF0YTogcmVzcG9uc2UucmVzcG9uc2VEYXRhLFxuICAgICAgICAgIHJlc3BvbnNlX3Rva2Vuc19wcm9tcHQ6IHJlc3BvbnNlLnRva2Vuc1Byb21wdCxcbiAgICAgICAgICByZXNwb25zZV90b2tlbnNfY29tcGxldGlvbjogcmVzcG9uc2UudG9rZW5zQ29tcGxldGlvbixcbiAgICAgICAgICByZXNwb25zZV9jb3N0OiByZXNwb25zZS5jb3N0LFxuICAgICAgICAgIGNhY2hlX3RpZXI6IHRpZXIsXG4gICAgICAgICAgZXhwaXJlc19hdDogZXhwaXJlc0F0LnRvSVNPU3RyaW5nKClcbiAgICAgICAgfSk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbU2VtYW50aWMgQ2FjaGVdIFN0b3JlIGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhgW1NlbWFudGljIENhY2hlXSBTdG9yZWQgcmVzcG9uc2UgZm9yICR7dGllcn0gdGllciAoZXhwaXJlczogJHtleHBpcmVzQXQudG9JU09TdHJpbmcoKX0pYCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbU2VtYW50aWMgQ2FjaGVdIFN0b3JlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IGNhY2hlIHN0YXRpc3RpY3MgZm9yIGEgdXNlci9jb25maWdcbiAgICovXG4gIGFzeW5jIGdldENhY2hlU3RhdHMoXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgY29uZmlnSWQ6IHN0cmluZyxcbiAgICBkYXlzOiBudW1iZXIgPSA3XG4gICk6IFByb21pc2U8Q2FjaGVTdGF0cz4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZpY2VSb2xlQ2xpZW50KCk7XG4gICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSgpO1xuICAgICAgc3RhcnREYXRlLnNldERhdGUoc3RhcnREYXRlLmdldERhdGUoKSAtIGRheXMpO1xuXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnc2VtYW50aWNfY2FjaGVfYW5hbHl0aWNzJylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgICAgLmVxKCdjdXN0b21fYXBpX2NvbmZpZ19pZCcsIGNvbmZpZ0lkKVxuICAgICAgICAuZ3RlKCdkYXRlJywgc3RhcnREYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbU2VtYW50aWMgQ2FjaGVdIFN0YXRzIGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0RW1wdHlTdGF0cygpO1xuICAgICAgfVxuXG4gICAgICBpZiAoIWRhdGEgfHwgZGF0YS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0RW1wdHlTdGF0cygpO1xuICAgICAgfVxuXG4gICAgICAvLyBBZ2dyZWdhdGUgc3RhdGlzdGljc1xuICAgICAgY29uc3QgdG90YWxzID0gZGF0YS5yZWR1Y2UoKGFjYywgcm93KSA9PiAoe1xuICAgICAgICB0b3RhbFJlcXVlc3RzOiBhY2MudG90YWxSZXF1ZXN0cyArIChyb3cudG90YWxfcmVxdWVzdHMgfHwgMCksXG4gICAgICAgIGNhY2hlSGl0czogYWNjLmNhY2hlSGl0cyArIChyb3cuY2FjaGVfaGl0cyB8fCAwKSxcbiAgICAgICAgY2FjaGVNaXNzZXM6IGFjYy5jYWNoZU1pc3NlcyArIChyb3cuY2FjaGVfbWlzc2VzIHx8IDApLFxuICAgICAgICB0b2tlbnNTYXZlZDogYWNjLnRva2Vuc1NhdmVkICsgKHJvdy50b2tlbnNfc2F2ZWQgfHwgMCksXG4gICAgICAgIGNvc3RTYXZlZDogYWNjLmNvc3RTYXZlZCArIChyb3cuY29zdF9zYXZlZCB8fCAwKSxcbiAgICAgICAgcmVzcG9uc2VUaW1lU3VtOiBhY2MucmVzcG9uc2VUaW1lU3VtICsgKHJvdy5hdmdfcmVzcG9uc2VfdGltZV9tcyB8fCAwKSxcbiAgICAgICAgcm93Q291bnQ6IGFjYy5yb3dDb3VudCArIDFcbiAgICAgIH0pLCB7XG4gICAgICAgIHRvdGFsUmVxdWVzdHM6IDAsXG4gICAgICAgIGNhY2hlSGl0czogMCxcbiAgICAgICAgY2FjaGVNaXNzZXM6IDAsXG4gICAgICAgIHRva2Vuc1NhdmVkOiAwLFxuICAgICAgICBjb3N0U2F2ZWQ6IDAsXG4gICAgICAgIHJlc3BvbnNlVGltZVN1bTogMCxcbiAgICAgICAgcm93Q291bnQ6IDBcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICB0b3RhbFJlcXVlc3RzOiB0b3RhbHMudG90YWxSZXF1ZXN0cyxcbiAgICAgICAgY2FjaGVIaXRzOiB0b3RhbHMuY2FjaGVIaXRzLFxuICAgICAgICBjYWNoZU1pc3NlczogdG90YWxzLmNhY2hlTWlzc2VzLFxuICAgICAgICBoaXRSYXRlOiB0b3RhbHMudG90YWxSZXF1ZXN0cyA+IDAgPyB0b3RhbHMuY2FjaGVIaXRzIC8gdG90YWxzLnRvdGFsUmVxdWVzdHMgOiAwLFxuICAgICAgICB0b2tlbnNTYXZlZDogdG90YWxzLnRva2Vuc1NhdmVkLFxuICAgICAgICBjb3N0U2F2ZWQ6IHRvdGFscy5jb3N0U2F2ZWQsXG4gICAgICAgIGF2Z1Jlc3BvbnNlVGltZTogdG90YWxzLnJvd0NvdW50ID4gMCA/IHRvdGFscy5yZXNwb25zZVRpbWVTdW0gLyB0b3RhbHMucm93Q291bnQgOiAwXG4gICAgICB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tTZW1hbnRpYyBDYWNoZV0gU3RhdHMgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHRoaXMuZ2V0RW1wdHlTdGF0cygpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDbGVhbiB1cCBleHBpcmVkIGNhY2hlIGVudHJpZXNcbiAgICovXG4gIGFzeW5jIGNsZWFudXBFeHBpcmVkQ2FjaGUoKTogUHJvbWlzZTxudW1iZXI+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2aWNlUm9sZUNsaWVudCgpO1xuICAgICAgXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAucnBjKCdjbGVhbnVwX3NlbWFudGljX2NhY2hlJyk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbU2VtYW50aWMgQ2FjaGVdIENsZWFudXAgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gMDtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYFtTZW1hbnRpYyBDYWNoZV0gQ2xlYW5lZCB1cCAke2RhdGF9IGV4cGlyZWQgZW50cmllc2ApO1xuICAgICAgcmV0dXJuIGRhdGEgfHwgMDtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbU2VtYW50aWMgQ2FjaGVdIENsZWFudXAgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIDA7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFByaXZhdGUgaGVscGVyIG1ldGhvZHNcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgaW5jcmVtZW50SGl0Q291bnQoY2FjaGVJZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU2VydmljZVJvbGVDbGllbnQoKTtcbiAgICAgIGF3YWl0IHN1cGFiYXNlLnJwYygnaW5jcmVtZW50X2NhY2hlX2hpdCcsIHsgY2FjaGVfaWQ6IGNhY2hlSWQgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tTZW1hbnRpYyBDYWNoZV0gSGl0IGNvdW50IGluY3JlbWVudCBlcnJvcjonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBmb3JtYXRDYWNoZWRSZXN1bHQoY2FjaGVFbnRyeTogYW55LCBzaW1pbGFyaXR5OiBudW1iZXIpOiBDYWNoZWRSZXN1bHQge1xuICAgIHJldHVybiB7XG4gICAgICBpZDogY2FjaGVFbnRyeS5pZCxcbiAgICAgIHByb21wdFRleHQ6IGNhY2hlRW50cnkucHJvbXB0X3RleHQsXG4gICAgICByZXNwb25zZURhdGE6IGNhY2hlRW50cnkucmVzcG9uc2VfZGF0YSxcbiAgICAgIG1vZGVsVXNlZDogY2FjaGVFbnRyeS5tb2RlbF91c2VkLFxuICAgICAgcHJvdmlkZXJVc2VkOiBjYWNoZUVudHJ5LnByb3ZpZGVyX3VzZWQsXG4gICAgICBzaW1pbGFyaXR5LFxuICAgICAgaGl0Q291bnQ6IGNhY2hlRW50cnkuaGl0X2NvdW50LFxuICAgICAgY3JlYXRlZEF0OiBjYWNoZUVudHJ5LmNyZWF0ZWRfYXQsXG4gICAgICBleHBpcmVzQXQ6IGNhY2hlRW50cnkuZXhwaXJlc19hdFxuICAgIH07XG4gIH1cblxuICBwcml2YXRlIGdldEVtcHR5U3RhdHMoKTogQ2FjaGVTdGF0cyB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRvdGFsUmVxdWVzdHM6IDAsXG4gICAgICBjYWNoZUhpdHM6IDAsXG4gICAgICBjYWNoZU1pc3NlczogMCxcbiAgICAgIGhpdFJhdGU6IDAsXG4gICAgICB0b2tlbnNTYXZlZDogMCxcbiAgICAgIGNvc3RTYXZlZDogMCxcbiAgICAgIGF2Z1Jlc3BvbnNlVGltZTogMFxuICAgIH07XG4gIH1cbn1cblxuLy8gRXhwb3J0IHNpbmdsZXRvbiBpbnN0YW5jZVxuZXhwb3J0IGNvbnN0IHNlbWFudGljQ2FjaGUgPSBTZW1hbnRpY0NhY2hlU2VydmljZS5nZXRJbnN0YW5jZSgpO1xuIl0sIm5hbWVzIjpbImppbmFFbWJlZGRpbmdzIiwiY3JlYXRlQ2xpZW50IiwiY3J5cHRvIiwiY3JlYXRlU2VydmljZVJvbGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsImF1dGgiLCJhdXRvUmVmcmVzaFRva2VuIiwicGVyc2lzdFNlc3Npb24iLCJTZW1hbnRpY0NhY2hlU2VydmljZSIsInRpZXJDb25maWciLCJmcmVlIiwiZW5hYmxlZCIsInR0bEhvdXJzIiwic2ltaWxhcml0eVRocmVzaG9sZCIsIm1heENhY2hlU2l6ZSIsInN0YXJ0ZXIiLCJwcm8iLCJlbnRlcnByaXNlIiwiZ2V0SW5zdGFuY2UiLCJpbnN0YW5jZSIsImdlbmVyYXRlUHJvbXB0SGFzaCIsInByb21wdCIsIm1vZGVsIiwidGVtcGVyYXR1cmUiLCJoYXNoSW5wdXQiLCJjcmVhdGVIYXNoIiwidXBkYXRlIiwiZGlnZXN0IiwiaXNDYWNoZUVuYWJsZWQiLCJ0aWVyIiwiZ2V0VGllckNvbmZpZyIsInNlYXJjaENhY2hlIiwicmVxdWVzdCIsInVzZXJJZCIsImNvbmZpZ0lkIiwiY29uZmlnIiwic3VwYWJhc2UiLCJwcm9tcHRIYXNoIiwicHJvbXB0VGV4dCIsIm1vZGVsVXNlZCIsImRhdGEiLCJleGFjdE1hdGNoIiwiZXJyb3IiLCJleGFjdEVycm9yIiwiZnJvbSIsInNlbGVjdCIsImVxIiwiZ3QiLCJEYXRlIiwidG9JU09TdHJpbmciLCJsaW1pdCIsInNpbmdsZSIsImNvbnNvbGUiLCJsb2ciLCJpbmNyZW1lbnRIaXRDb3VudCIsImlkIiwiZm9ybWF0Q2FjaGVkUmVzdWx0IiwicXVlcnlFbWJlZGRpbmciLCJlbWJlZFF1ZXJ5Iiwic2VtYW50aWNNYXRjaGVzIiwic2VtYW50aWNFcnJvciIsInJwYyIsInF1ZXJ5X2VtYmVkZGluZyIsImNvbmZpZ19pZCIsInVzZXJfaWRfcGFyYW0iLCJzaW1pbGFyaXR5X3RocmVzaG9sZCIsIm1hdGNoX2NvdW50IiwibGVuZ3RoIiwibWF0Y2giLCJzaW1pbGFyaXR5IiwidG9GaXhlZCIsInN0b3JlQ2FjaGUiLCJyZXNwb25zZSIsInByb21wdEVtYmVkZGluZyIsImV4cGlyZXNBdCIsInNldEhvdXJzIiwiZ2V0SG91cnMiLCJpbnNlcnQiLCJ1c2VyX2lkIiwiY3VzdG9tX2FwaV9jb25maWdfaWQiLCJwcm9tcHRfdGV4dCIsInByb21wdF9lbWJlZGRpbmciLCJwcm9tcHRfaGFzaCIsIm1vZGVsX3VzZWQiLCJwcm92aWRlcl91c2VkIiwicHJvdmlkZXJVc2VkIiwibWF4X3Rva2VucyIsIm1heFRva2VucyIsInJlcXVlc3RfbWV0YWRhdGEiLCJtZXRhZGF0YSIsInJlc3BvbnNlX2RhdGEiLCJyZXNwb25zZURhdGEiLCJyZXNwb25zZV90b2tlbnNfcHJvbXB0IiwidG9rZW5zUHJvbXB0IiwicmVzcG9uc2VfdG9rZW5zX2NvbXBsZXRpb24iLCJ0b2tlbnNDb21wbGV0aW9uIiwicmVzcG9uc2VfY29zdCIsImNvc3QiLCJjYWNoZV90aWVyIiwiZXhwaXJlc19hdCIsImdldENhY2hlU3RhdHMiLCJkYXlzIiwic3RhcnREYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJndGUiLCJzcGxpdCIsImdldEVtcHR5U3RhdHMiLCJ0b3RhbHMiLCJyZWR1Y2UiLCJhY2MiLCJyb3ciLCJ0b3RhbFJlcXVlc3RzIiwidG90YWxfcmVxdWVzdHMiLCJjYWNoZUhpdHMiLCJjYWNoZV9oaXRzIiwiY2FjaGVNaXNzZXMiLCJjYWNoZV9taXNzZXMiLCJ0b2tlbnNTYXZlZCIsInRva2Vuc19zYXZlZCIsImNvc3RTYXZlZCIsImNvc3Rfc2F2ZWQiLCJyZXNwb25zZVRpbWVTdW0iLCJhdmdfcmVzcG9uc2VfdGltZV9tcyIsInJvd0NvdW50IiwiaGl0UmF0ZSIsImF2Z1Jlc3BvbnNlVGltZSIsImNsZWFudXBFeHBpcmVkQ2FjaGUiLCJjYWNoZUlkIiwiY2FjaGVfaWQiLCJjYWNoZUVudHJ5IiwiaGl0Q291bnQiLCJoaXRfY291bnQiLCJjcmVhdGVkQXQiLCJjcmVhdGVkX2F0Iiwic2VtYW50aWNDYWNoZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/semantic-cache/SemanticCacheService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/semantic-cache/tierUtils.ts":
/*!*********************************************!*\
  !*** ./src/lib/semantic-cache/tierUtils.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canStoreMoreCacheEntries: () => (/* binding */ canStoreMoreCacheEntries),\n/* harmony export */   getCacheLimits: () => (/* binding */ getCacheLimits),\n/* harmony export */   getTierDisplayInfo: () => (/* binding */ getTierDisplayInfo),\n/* harmony export */   getUserTier: () => (/* binding */ getUserTier),\n/* harmony export */   getUserTierInfo: () => (/* binding */ getUserTierInfo),\n/* harmony export */   hasSemanticCacheAccess: () => (/* binding */ hasSemanticCacheAccess),\n/* harmony export */   logTierEvent: () => (/* binding */ logTierEvent)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Tier Utilities for Semantic Caching\n * Determines user subscription tier and cache permissions\n */ \n// Create service role client for server-side operations that need to bypass RLS\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n/**\n * Get user's subscription tier from subscriptions table\n */ async function getUserTier(userId) {\n    try {\n        const supabase = createServiceRoleClient();\n        // Get user's subscription information from subscriptions table\n        const { data: subscription, error } = await supabase.from('subscriptions').select('tier, status').eq('user_id', userId).eq('status', 'active').single();\n        if (error || !subscription) {\n            console.log('[Tier Utils] No active subscription found, defaulting to free tier');\n            return 'free';\n        }\n        // Map subscription tier to cache tier\n        const subscriptionTierToCache = {\n            'starter': 'starter',\n            'professional': 'pro',\n            'enterprise': 'enterprise'\n        };\n        const tier = subscriptionTierToCache[subscription.tier] || 'free';\n        console.log(`[Tier Utils] User ${userId} has tier: ${tier} (subscription_tier: ${subscription.tier})`);\n        return tier;\n    } catch (error) {\n        console.error('[Tier Utils] Error getting user tier:', error);\n        return 'free';\n    }\n}\n/**\n * Get comprehensive tier information including cache settings\n */ async function getUserTierInfo(userId) {\n    const tier = await getUserTier(userId);\n    const tierConfigs = {\n        free: {\n            tier: 'free',\n            semanticCacheEnabled: false,\n            maxCacheEntries: 0,\n            cacheTTLHours: 0\n        },\n        starter: {\n            tier: 'starter',\n            semanticCacheEnabled: false,\n            maxCacheEntries: 0,\n            cacheTTLHours: 0\n        },\n        pro: {\n            tier: 'pro',\n            semanticCacheEnabled: true,\n            maxCacheEntries: 1000,\n            cacheTTLHours: 24 // 1 day\n        },\n        enterprise: {\n            tier: 'enterprise',\n            semanticCacheEnabled: true,\n            maxCacheEntries: 10000,\n            cacheTTLHours: 168 // 1 week\n        }\n    };\n    return tierConfigs[tier];\n}\n/**\n * Check if user has access to semantic caching\n */ async function hasSemanticCacheAccess(userId) {\n    const tierInfo = await getUserTierInfo(userId);\n    return tierInfo.semanticCacheEnabled;\n}\n/**\n * Get cache limits for a user\n */ async function getCacheLimits(userId) {\n    const tierInfo = await getUserTierInfo(userId);\n    return {\n        maxEntries: tierInfo.maxCacheEntries,\n        ttlHours: tierInfo.cacheTTLHours,\n        enabled: tierInfo.semanticCacheEnabled\n    };\n}\n/**\n * Validate if user can store more cache entries\n */ async function canStoreMoreCacheEntries(userId, configId) {\n    try {\n        const tierInfo = await getUserTierInfo(userId);\n        if (!tierInfo.semanticCacheEnabled) {\n            return false;\n        }\n        // If unlimited (enterprise with very high limit), allow\n        if (tierInfo.maxCacheEntries >= 10000) {\n            return true;\n        }\n        // Check current cache entry count\n        const supabase = createServiceRoleClient();\n        const { count, error } = await supabase.from('semantic_cache').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId).eq('custom_api_config_id', configId).gt('expires_at', new Date().toISOString());\n        if (error) {\n            console.error('[Tier Utils] Error checking cache count:', error);\n            return false;\n        }\n        return (count || 0) < tierInfo.maxCacheEntries;\n    } catch (error) {\n        console.error('[Tier Utils] Error validating cache storage:', error);\n        return false;\n    }\n}\n/**\n * Get tier display information for UI\n */ function getTierDisplayInfo(tier) {\n    const displayInfo = {\n        free: {\n            name: 'Free',\n            color: 'gray',\n            features: [\n                'Basic routing',\n                'Simple caching',\n                'Community support'\n            ]\n        },\n        starter: {\n            name: 'Starter',\n            color: 'blue',\n            features: [\n                'Limited role routing',\n                'Simple caching',\n                'Prompt engineering'\n            ]\n        },\n        pro: {\n            name: 'Professional',\n            color: 'orange',\n            features: [\n                'Semantic caching',\n                'Advanced routing',\n                'Knowledge base',\n                'Priority support'\n            ]\n        },\n        enterprise: {\n            name: 'Enterprise',\n            color: 'purple',\n            features: [\n                'Advanced semantic caching',\n                'Custom rules',\n                'Team management',\n                'Dedicated support'\n            ]\n        }\n    };\n    return displayInfo[tier];\n}\n/**\n * Log tier-related events for analytics\n */ async function logTierEvent(userId, event, metadata) {\n    try {\n        const tier = await getUserTier(userId);\n        console.log(`[Tier Analytics] User ${userId} (${tier}): ${event}`, metadata);\n    // Here you could store analytics in a separate table if needed\n    // For now, we'll just log to console\n    } catch (error) {\n        console.error('[Tier Utils] Error logging tier event:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/semantic-cache/tierUtils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest%2Fsemantic-cache%2Froute&page=%2Fapi%2Ftest%2Fsemantic-cache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest%2Fsemantic-cache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();