/**
 * Tier Utilities for Semantic Caching
 * Determines user subscription tier and cache permissions
 */

import { createClient } from '@supabase/supabase-js';
import type { CacheTier } from './SemanticCacheService';

// Create service role client for server-side operations that need to bypass RLS
function createServiceRoleClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export interface UserTierInfo {
  tier: CacheTier;
  semanticCacheEnabled: boolean;
  maxCacheEntries: number;
  cacheTTLHours: number;
}

/**
 * Get user's subscription tier from subscriptions table
 */
export async function getUserTier(userId: string): Promise<CacheTier> {
  try {
    const supabase = createServiceRoleClient();

    // Get user's subscription information from subscriptions table
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select('tier, status')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error || !subscription) {
      console.log('[Tier Utils] No active subscription found, defaulting to free tier');
      return 'free';
    }

    // Map subscription tier to cache tier
    const subscriptionTierToCache: Record<string, CacheTier> = {
      'starter': 'starter',
      'professional': 'pro',  // Map 'professional' to 'pro' for cache system
      'enterprise': 'enterprise'
    };

    const tier = subscriptionTierToCache[subscription.tier] || 'free';
    console.log(`[Tier Utils] User ${userId} has tier: ${tier} (subscription_tier: ${subscription.tier})`);

    return tier;

  } catch (error) {
    console.error('[Tier Utils] Error getting user tier:', error);
    return 'free';
  }
}

/**
 * Get comprehensive tier information including cache settings
 */
export async function getUserTierInfo(userId: string): Promise<UserTierInfo> {
  const tier = await getUserTier(userId);
  
  const tierConfigs: Record<CacheTier, UserTierInfo> = {
    free: {
      tier: 'free',
      semanticCacheEnabled: false,
      maxCacheEntries: 0,
      cacheTTLHours: 0
    },
    starter: {
      tier: 'starter',
      semanticCacheEnabled: false, // Only simple caching for starter
      maxCacheEntries: 0,
      cacheTTLHours: 0
    },
    pro: {
      tier: 'pro',
      semanticCacheEnabled: true,
      maxCacheEntries: 1000,
      cacheTTLHours: 24 // 1 day
    },
    enterprise: {
      tier: 'enterprise',
      semanticCacheEnabled: true,
      maxCacheEntries: 10000,
      cacheTTLHours: 168 // 1 week
    }
  };

  return tierConfigs[tier];
}

/**
 * Check if user has access to semantic caching
 */
export async function hasSemanticCacheAccess(userId: string): Promise<boolean> {
  const tierInfo = await getUserTierInfo(userId);
  return tierInfo.semanticCacheEnabled;
}

/**
 * Get cache limits for a user
 */
export async function getCacheLimits(userId: string): Promise<{
  maxEntries: number;
  ttlHours: number;
  enabled: boolean;
}> {
  const tierInfo = await getUserTierInfo(userId);
  
  return {
    maxEntries: tierInfo.maxCacheEntries,
    ttlHours: tierInfo.cacheTTLHours,
    enabled: tierInfo.semanticCacheEnabled
  };
}

/**
 * Validate if user can store more cache entries
 */
export async function canStoreMoreCacheEntries(
  userId: string,
  configId: string
): Promise<boolean> {
  try {
    const tierInfo = await getUserTierInfo(userId);
    
    if (!tierInfo.semanticCacheEnabled) {
      return false;
    }

    // If unlimited (enterprise with very high limit), allow
    if (tierInfo.maxCacheEntries >= 10000) {
      return true;
    }

    // Check current cache entry count
    const supabase = createServiceRoleClient();
    const { count, error } = await supabase
      .from('semantic_cache')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('custom_api_config_id', configId)
      .gt('expires_at', new Date().toISOString());

    if (error) {
      console.error('[Tier Utils] Error checking cache count:', error);
      return false;
    }

    return (count || 0) < tierInfo.maxCacheEntries;

  } catch (error) {
    console.error('[Tier Utils] Error validating cache storage:', error);
    return false;
  }
}

/**
 * Get tier display information for UI
 */
export function getTierDisplayInfo(tier: CacheTier): {
  name: string;
  color: string;
  features: string[];
} {
  const displayInfo = {
    free: {
      name: 'Free',
      color: 'gray',
      features: ['Basic routing', 'Simple caching', 'Community support']
    },
    starter: {
      name: 'Starter',
      color: 'blue',
      features: ['Limited role routing', 'Simple caching', 'Prompt engineering']
    },
    pro: {
      name: 'Professional',
      color: 'orange',
      features: ['Semantic caching', 'Advanced routing', 'Knowledge base', 'Priority support']
    },
    enterprise: {
      name: 'Enterprise',
      color: 'purple',
      features: ['Advanced semantic caching', 'Custom rules', 'Team management', 'Dedicated support']
    }
  };

  return displayInfo[tier];
}

/**
 * Log tier-related events for analytics
 */
export async function logTierEvent(
  userId: string,
  event: 'cache_hit' | 'cache_miss' | 'cache_store' | 'tier_check',
  metadata?: Record<string, any>
): Promise<void> {
  try {
    const tier = await getUserTier(userId);
    
    console.log(`[Tier Analytics] User ${userId} (${tier}): ${event}`, metadata);
    
    // Here you could store analytics in a separate table if needed
    // For now, we'll just log to console
    
  } catch (error) {
    console.error('[Tier Utils] Error logging tier event:', error);
  }
}
